// 注意‼️ 此文件更新需同步 bce-config.js 中的内容
// 注意‼️ 此文件更新需同步 bce-config.js 中的内容
// 注意‼️ 此文件更新需同步 bce-config.js 中的内容
const webpack = require('webpack');
const {defineConfig} = require('@baidu/cba-cli');
const MiniCssExtractPlugin = require('mini-css-extract-plugin');
const path = require('path');
const MonacoWebpackPlugin = require('monaco-editor-webpack-plugin');
const {config} = require('dotenv');
const env = config().parsed;

const templateId = '3a443985-cea0-4b7c-892e-6a826c46f4c5'; // 复用功能清单 【EDAP-私有化最新版】 项目

// 自定义本地服务 host 地址
const devHost = env.DEV_HOST;
// 自定义本地服务 端口
const devPort = env.DEV_PORT;
// 自定义代理地址（协议+域名+端口）
const proxyTarget = env.PROXY_TARGET;

module.exports = defineConfig({
  appName: 'databuilder',
  htmlTemplate: './private-common/private-index.html', // 路径是相对 process.cwd() 的
  presets: ['@baidu/cba-preset-react'],
  babelOptions: {
    plugins: ['@babel/plugin-proposal-optional-chaining'],
    resolveOptions: (babelConfig) => {
      babelConfig.presets = []; // 让其使用本地 .babelrc 配置
      return babelConfig;
    }
  },

  port: devPort,
  host: devHost,
  https: false,
  rules: ['/api/databuilder/(.*)'],
  root: '.mockrc',
  proxyTarget, // 代理到 DB 私有化测试环境
  publicPath: '/databuilder/', // 设置 webpack dev server 的根路径
  openPath: '/databuilder/', // 设置自动打开浏览器时的路径
  i18n: {
    enabled: true,
    independent: false
  },
  templateId, // 功能清单ID
  flags: ['databuilder'], // 功能清单项, 要配置 DB 产品的功能清单
  webpack(config, merge) {
    // 这里merge无法生效，需要手动合并
    config.module.rules[0].oneOf.unshift({
      test: /tailwind\.css$/,
      use: [
        MiniCssExtractPlugin.loader,
        'css-loader',
        {
          loader: 'postcss-loader',
          options: {
            postcssOptions: {
              plugins: ['tailwindcss', 'postcss-preset-env']
            }
          }
        }
      ]
    });

    // 解决jupyter svg文件加载问题
    config.module.rules[0].oneOf.unshift({
      // In .ts and .tsx files (both of which compile to .js), svg files
      // must be loaded as a raw string instead of data URIs.
      test: /\.svg(\?v=\d+\.\d+\.\d+)?$/,
      issuer: /node_modules\/@jupyterlab\/.*\.js$/,
      type: 'asset/source'
    });

    return merge(config, {
      module: {
        rules: [
          {
            // 此规则用于解决 webpack5 中的模块解析问题，针对 @baidu/xicon-react-bigdata 的解析问题
            test: /\.m?js/,
            resolve: {
              fullySpecified: false
            }
          }
        ]
      },
      devServer: {
        proxy: {
          '/databuilder/api/databuilder/v1': {
            target: proxyTarget,
            changeOrigin: true,
            secure: false,
            logLevel: 'debug'
          },
          '/pms/v1/user/loginStatus': {
            target: proxyTarget,
            changeOrigin: true,
            secure: false,
            logLevel: 'debug'
          },
          '/databuilder/api/databuilder/ws': {
            target: proxyTarget,
            changeOrigin: true,
            secure: false,
            ws: true,
            protocolRewrite: 'http',
            upgrade: true,
            pathRewrite: {'^/databuilder/api/databuilder/ws': '/databuilder/api/databuilder/ws'}, // 保持路径不变
            onProxyReq(proxyReq) {
              // 某些接口会验证origin
              proxyReq.setHeader('origin', proxyTarget);
            }
          }
        },
        static: [
          {
            directory: path.resolve(__dirname, '../public'),
            publicPath: '/databuilder/',
            serveIndex: false
          }
        ],
        open: {target: '/databuilder/'}
      },
      resolve: {
        alias: {
          // '@': path.resolve('./src'), // 注意：已经在 CBA webpack 基础配置中配过了
          '@pages': path.resolve('./src/pages'),
          '@components': path.resolve('./src/components'),
          '@hooks': path.resolve('./src/hooks'),
          '@utils': path.resolve('./src/utils'),
          '@api': path.resolve('./src/api'),
          '@assets': path.resolve('./src/assets'),
          '@styles': path.resolve('./src/styles'),
          '@type': path.resolve('./src/type'),
          '@store': path.resolve('./src/store'),
          '@helpers': path.resolve('./src/helpers')
        },
        // 解决prob-image-size包报错问题
        fallback: {
          stream: require.resolve('stream-browserify')
        }
      },
      plugins: [
        new MonacoWebpackPlugin(),
        new webpack.DefinePlugin({
          IS_DEV_ENV: JSON.stringify(process.env.NODE_ENV === 'development')
        })
      ]
    });
  }
});
