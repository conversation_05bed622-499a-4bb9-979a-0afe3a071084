# 直接使用公司提供的基础镜像 nginx 1.24.0 版本作为支持业务侧多 CPU 架构构建的基础镜像
FROM iregistry.baidu-int.com/hub-official/nginx:1.24.0

LABEL maintainer="<EMAIL>, <EMAIL>"

ENV LANG en_US.utf8

# 创建 configMap 中定义的  404.html 和 nginx.conf 存放的文件夹
RUN mkdir -p /usr/local/nginx/html
RUN mkdir -p /usr/local/nginx/conf

# 复制 Nginx 相关配置文件
# 可避免更改 nginx 配置文件中 include mime.types; 的配置
RUN cp -r /etc/nginx/* /usr/local/nginx/conf/

# 创建 产品业务代码 存放的文件夹
RUN mkdir -p /usr/share/output

COPY ./output /usr/share/output
COPY ./output/start.sh /root/

# 删除静态资源中的 shell 文件，
RUN rm -rf /usr/share/output/start.sh && \
    chmod 755 /root/start.sh

EXPOSE 80

WORKDIR /root/

# 注意：
# 1、天牛平台上默认使用镜像系统中自带的 start.sh 即可，如果非要通过 k8s 挂载卷注入该文件，请参考start.sh当前文件内容配置对应 configMap
# 2、其他 天牛平台上的配置项可无需调整，兼容历史项目
ENTRYPOINT ["./start.sh"]
