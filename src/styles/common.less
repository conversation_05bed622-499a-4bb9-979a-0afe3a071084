// 公共样式：请在各业务 less 文件中通过 @import 引入，避免重复定义。
// 例如：@import '~@styles/common.less';

// 表单元素的标题样式
.legend-title {
  align-items: center;
  color: #151b26;
  display: flex;
  font-size: 16px;
  font-weight: 500;
  min-height: 24px;
  margin-bottom: 20px;

  &::before {
    content: '';
    display: inline-block;
    width: 4px;
    height: 14px;
    background: #2468f2;
    border-radius: 0 2px 2px 0;
    transform: scaleX(-1) scaleY(-1);
    position: relative;
    margin-right: 8px;
  }
}
