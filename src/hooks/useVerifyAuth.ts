import {batchVerifyAuth, verifyAuth} from '@api/permission';
import {VerifyAuthParams} from '@api/permission/type';
import {useCallback, useEffect, useState} from 'react';

/**
 * 鉴权
 */
export function useVerifyAuth(resourceAuth: VerifyAuthParams[]) {
  const [auth, setAuth] = useState<boolean[]>([]);

  const getVerifyAuth = useCallback(async () => {
    // 批量鉴权
    if (resourceAuth?.length > 1) {
      const res = await batchVerifyAuth(resourceAuth);
      setAuth(res.result);
    } else {
      const res = await verifyAuth(resourceAuth[0]);
      setAuth([res.result]);
    }
  }, [resourceAuth]);

  useEffect(() => {
    getVerifyAuth();
  }, [getVerifyAuth]);

  return [auth];
}
