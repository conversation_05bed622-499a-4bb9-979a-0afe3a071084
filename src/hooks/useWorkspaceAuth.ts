import {useSelector} from 'react-redux';
import {IAppState} from '@store/index';
import {Privilege} from '@api/permission/type';

// 空间内权限
export default function useWorkspaceAuth(privileges: Privilege[]): Partial<Record<Privilege, boolean>> {
  const permission = useSelector((state: IAppState) => state.globalAuthSlice.workspacePermission);
  return privileges.reduce((pre, cur) => ({...pre, [cur]: permission ? permission?.[cur] : false}), {});
}
