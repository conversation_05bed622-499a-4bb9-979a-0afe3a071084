import React from 'react';
import moment from 'moment';
import {DatePicker, Input, Select, Space, Tooltip} from 'acud';
import {TimeFilterType, TimeFilterTypeOptions} from '../../constants';
import locale from 'acud/es/date-picker/locale/zh_CN';

// 动态时间预设选项
const DynamicTimeOptions = [
  {
    label: '当天 ${logicTime(yyyy-MM-dd HH:mm:ss)}',
    value: '${logicTime(yyyy-MM-dd HH:mm:ss)}'
  },
  {
    label: '前1天 ${logicTime(yyyy-MM-dd HH:mm:ss,-1d)}',
    value: '${logicTime(yyyy-MM-dd HH:mm:ss,-1d)}'
  },
  {
    label: '前1分钟 ${logicTime(yyyy-MM-dd HH:mm:ss,-1m)}',
    value: '${logicTime(yyyy-MM-dd HH:mm:ss,-1m)}'
  },
  {
    label: '前1小时 ${logicTime(yyyy-MM-dd HH:mm:ss,-1h)}',
    value: '${logicTime(yyyy-MM-dd HH:mm:ss,-1h)}'
  },
  {
    label: '前1月 ${logicTime(yyyy-MM-dd HH:mm:ss,-1M)}',
    value: '${logicTime(yyyy-MM-dd HH:mm:ss,-1M)}'
  },
  {
    label: '前1年 ${logicTime(yyyy-MM-dd HH:mm:ss,-1y)}',
    value: '${logicTime(yyyy-MM-dd HH:mm:ss,-1y)}'
  }
];

export interface TimeFilterInputProps {
  value?: {type: TimeFilterType; value: string};
  onChange?: (value: {type: TimeFilterType; value: string}) => void;
  placeholder?: string;
  disabledTypes?: TimeFilterType[];
  fieldLabel?: string;
}

// 时间选择组件
const TimeFilterInput: React.FC<TimeFilterInputProps> = ({
  value,
  onChange,
  placeholder,
  disabledTypes = [],
  fieldLabel
}) => {
  const currentType = value?.type || TimeFilterType.Fixed;
  const currentValue = value?.value || '';

  // 时间过滤类型在切换时清空值
  const handleTypeChange = (type: TimeFilterType) => {
    onChange?.({
      type,
      value: ''
    });
  };

  const handleValueChange = (val: string) => {
    onChange?.({
      type: currentType,
      value: val
    });
  };

  const renderInput = () => {
    switch (currentType) {
      case TimeFilterType.Fixed: {
        // 安全地解析 moment 时间
        const getMomentValue = () => {
          if (!currentValue) return undefined;
          const momentValue = moment(currentValue);
          return momentValue.isValid() ? momentValue : undefined;
        };

        return (
          <DatePicker
            showTime
            format="YYYY-MM-DD HH:mm:ss"
            placeholder={placeholder || '请选择时间'}
            value={getMomentValue()}
            onChange={(date, dateString) => handleValueChange(dateString)}
            style={{width: 'calc(100% - 113px)'}}
            locale={locale}
          />
        );
      }
      case TimeFilterType.Dynamic:
        return (
          <Input.AutoComplete
            placeholder={placeholder || '${logicTime(yyyy-MM-dd HH:mm:ss,-1d)}'}
            value={currentValue}
            onChange={handleValueChange}
            onSelect={handleValueChange}
            options={DynamicTimeOptions}
            style={{width: 'calc(100% - 113px)'}}
          />
        );
      case TimeFilterType.Unlimited:
        return null;
      default:
        return null;
    }
  };

  // 过滤掉被禁用的选项
  const availableOptions = TimeFilterTypeOptions.map((option) => {
    const isDisabled = disabledTypes.includes(option.value as TimeFilterType);

    return {
      ...option,
      disabled: isDisabled
    };
  });

  // 自定义选项渲染函数
  const optionRender = (label: React.ReactNode, option: any) => {
    const isDisabled = option.disabled;
    const isUnlimitedDisabled = isDisabled && option.value === TimeFilterType.Unlimited;

    const content = <span>{label}</span>;

    // 如果是被禁用的"不限"选项，用 Tooltip 包裹
    if (isUnlimitedDisabled && fieldLabel) {
      return (
        <Tooltip title={`已选择${fieldLabel === '开始时间' ? '结束' : '开始'}时间不限`} placement="right">
          {content}
        </Tooltip>
      );
    }

    return content;
  };

  return (
    <>
      <Select
        value={currentType}
        onChange={handleTypeChange}
        options={availableOptions}
        optionRender={optionRender}
        style={{width: 105}}
        className="mr-[8px]"
      />
      {renderInput()}
    </>
  );
};

export default TimeFilterInput;
