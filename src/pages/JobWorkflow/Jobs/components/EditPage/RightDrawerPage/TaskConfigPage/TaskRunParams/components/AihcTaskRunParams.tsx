import React from 'react';
import {aihcQueuesList, aihcResourcepoolsList} from '@api/workflow';
import EditableContent from '@components/EditableContent';
import RemoteSelect from '@components/RemoteSelect';
import {WorkspaceContext} from '@pages/index';
import {IAppState} from '@store/index';
import {Form} from 'acud';
import {FormInstance} from 'acud/lib/form';
import {useContext} from 'react';
import {useSelector} from 'react-redux';

// 任务运行参数
const AihcTaskRunParams: React.FC<{form?: FormInstance}> = ({form}) => {
  const {workspaceId} = useContext(WorkspaceContext);
  // 是否编辑
  const isEditing = useSelector((state: IAppState) => state.workflowSlice.isEditing);
  const resourcePoolId = Form.useWatch('resourcePoolId', form);

  return (
    <>
      <Form.Item label="资源池" name="resourcePoolId">
        <EditableContent isEditing={isEditing}>
          <RemoteSelect
            showSearch={true}
            dropdownSearch={true}
            optionFilterProp="label"
            objId="jobId"
            dropdownMatchSelectWidth={false}
            showTitle={true}
            dropdownStyle={{maxWidth: 300}}
            queryList={aihcResourcepoolsList}
            params={[workspaceId]}
            placeholder="请选择"
          />
        </EditableContent>
      </Form.Item>
      <Form.Item label="队列" name="queue">
        <EditableContent isEditing={isEditing}>
          <RemoteSelect
            showSearch={true}
            dropdownSearch={true}
            optionFilterProp="label"
            objId="name"
            dropdownMatchSelectWidth={false}
            showTitle={true}
            dropdownStyle={{maxWidth: 300}}
            queryList={aihcQueuesList}
            params={[workspaceId, resourcePoolId]}
            placeholder="请选择"
          />
        </EditableContent>
      </Form.Item>
    </>
  );
};

export default AihcTaskRunParams;
