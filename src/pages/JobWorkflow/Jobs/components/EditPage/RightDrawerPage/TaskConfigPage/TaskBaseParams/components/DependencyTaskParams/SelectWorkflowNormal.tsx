import {detailJob, queryJob<PERSON>ist} from '@api/job';
import {Ellipsis} from '@baidu/bce-react-toolkit';
import EditableContent from '@components/EditableContent';
import RemoteSelect from '@components/RemoteSelect';
import {JobDependencyTypeChineseMap, JobDependencyTypeEnum} from '@pages/JobWorkflow/constants';
import {IAppState} from '@store/index';
import {Form, Select} from 'acud';
import {FormInstance} from 'acud/lib/form';
import {useMemoizedFn} from 'ahooks';
import React, {useEffect, useMemo, useState} from 'react';
import {useSelector} from 'react-redux';
import {IJsonNodeData} from '@pages/JobWorkflow/Jobs/components/EditPage/EditContent/X6EditPage/type';
import {nodeMap} from '@pages/JobWorkflow/Jobs/components/EditPage/globalVar';

import {Privilege} from '@api/permission/type';
import {queryWorkspaceDetail, queryWorkspaceList} from '@api/workspace';

const SelectWorkflowPrivate: React.FC<{form: FormInstance}> = ({form}) => {
  // 是否编辑
  const isEditing = useSelector((state: IAppState) => state.workflowSlice.isEditing);
  const selectedNodeId = useSelector((state: IAppState) => state.workflowSlice.selectedNodeId);

  const [dependencyType, setDependencyType] = useState<JobDependencyTypeEnum>(
    JobDependencyTypeEnum.DEPENDENT_JOB
  );

  const [jobNode, setJobNode] = useState<React.ReactNode>('-');
  const [taskNode, setTaskNode] = useState<React.ReactNode>('-');

  const [jobCode, setJobCode] = useState<string>('');

  // 依赖任务选项
  const depTaskIdOptions = useMemo(() => {
    if (!jobCode) {
      return [];
    }
    const arr = [];
    try {
      const jsonObj = JSON.parse(jobCode || '{}');
      jsonObj?.taskDefinitionList?.forEach((item) => {
        arr.push({label: item?.name, value: item?.id});
      });
    } catch (error) {
      console.error(error);
    }
    return arr;
  }, [jobCode]);

  // 处理工作流名称
  const dealJobId = useMemoizedFn(async (workspaceId: string, jobId: string, taskId?: string) => {
    if (!workspaceId || !jobId) {
      setJobNode('-');
      return;
    }

    const {result} = await detailJob(workspaceId, jobId);

    let taskNodeName = '-';
    try {
      const jsonObj = JSON.parse(result?.code || '{}');
      setJobCode(result?.code);

      for (const item of jsonObj?.taskDefinitionList || []) {
        if (taskId === item.id) {
          taskNodeName = item.name;
        }
      }
    } catch (error) {
      console.error(error);
    }
    setTaskNode(<Ellipsis tooltip={taskNodeName}>{taskNodeName}</Ellipsis>);
    setJobNode(<Ellipsis tooltip={result?.name}>{result?.name}</Ellipsis>);
  });

  // 初始化数据
  useEffect(() => {
    const obj = nodeMap.get(selectedNodeId) as IJsonNodeData;
    const taskParam: any = obj?.taskParam;

    dealJobId(taskParam?.workspaceId, taskParam?.jobId, taskParam?.depTaskId);

    setDependencyType(
      taskParam?.depTaskId ? JobDependencyTypeEnum.DEPENDENT_TASK : JobDependencyTypeEnum.DEPENDENT_JOB
    );
  }, [isEditing, selectedNodeId]);

  // 修改任务参数表单
  const changeTaskParam = useMemoizedFn(async (key: string, value: any) => {
    form.setFieldValue(key, value);
    const oldObj = nodeMap.get(selectedNodeId) as IJsonNodeData;

    const newObj = {
      ...oldObj,
      taskParam: {
        ...oldObj.taskParam,
        [key]: value
      }
    };
    nodeMap.set(selectedNodeId, newObj);
  });

  // 处理工作流名称改变
  const handleJobChange = useMemoizedFn((v: any) => {
    changeTaskParam('depTaskId', null);
    dealJobId(form.getFieldValue('workspaceId'), v);
  });

  //
  const onChangeSelectWorkspace = useMemoizedFn(() => {
    changeTaskParam('depTaskId', null);
    setDependencyType(JobDependencyTypeEnum.DEPENDENT_JOB);
  });

  return (
    <>
      <Form.Item label="工作空间" name="workspaceId" rules={[{required: true, message: '请选择工作空间'}]}>
        <EditableContent
          isEditing={isEditing}
          getDetail={(value) =>
            queryWorkspaceDetail({
              id: value
            }).then((res) => {
              return res.result.name;
            })
          }
        >
          <RemoteSelect
            showSearch={true}
            dropdownSearch={true}
            optionFilterProp="label"
            dropdownMatchSelectWidth={false}
            showTitle={true}
            dropdownStyle={{maxWidth: 300}}
            queryList={queryWorkspaceList}
            params={[{pageSize: 10000, pageNo: 1}]}
            onChangeSelect={() => {
              onChangeSelectWorkspace();
            }}
            disabledOptionFn={(v) => {
              return !v.privileges.includes(Privilege.WorkflowMenu);
            }}
            placeholder="全部工作空间"
          />
        </EditableContent>
      </Form.Item>

      <Form.Item label="依赖类型">
        {isEditing ? (
          <Select
            className="w-full"
            defaultValue={dependencyType}
            value={dependencyType}
            onSelect={(v) => {
              setDependencyType(v as JobDependencyTypeEnum);
              changeTaskParam('depTaskId', null);
            }}
            options={Object.entries(JobDependencyTypeChineseMap).map(([key, value]) => ({
              label: value,
              value: key
            }))}
          />
        ) : (
          JobDependencyTypeChineseMap[dependencyType]
        )}
      </Form.Item>
      <Form.Item
        shouldUpdate={(prevValues, curValues) => prevValues.workspaceId !== curValues.workspaceId}
        noStyle
      >
        {({getFieldValue}) => {
          const workspaceId = getFieldValue('workspaceId');
          const params = [{pageSize: 10000, pageNo: 1}, workspaceId];

          return (
            <Form.Item label="工作流名称" name="jobId" rules={[{required: true, message: '请选择工作流'}]}>
              <EditableContent isEditing={isEditing} dealValue={() => jobNode}>
                <RemoteSelect
                  showSearch
                  dropdownSearch={true}
                  optionFilterProp="label"
                  dropdownMatchSelectWidth={false}
                  showTitle={true}
                  dropdownStyle={{maxWidth: 300}}
                  disabled={!workspaceId}
                  objName="name"
                  objId="jobId"
                  queryList={queryJobList}
                  params={params}
                  placeholder="全部工作流"
                  onSelect={handleJobChange}
                />
              </EditableContent>
            </Form.Item>
          );
        }}
      </Form.Item>
      {dependencyType === JobDependencyTypeEnum.DEPENDENT_TASK && (
        <Form.Item shouldUpdate={(prevValues, curValues) => prevValues.jobId !== curValues.jobId} noStyle>
          {() => {
            return (
              <>
                <Form.Item label="任务名称" name="depTaskId">
                  <EditableContent isEditing={isEditing} dealValue={() => taskNode}>
                    <Select
                      showSearch
                      optionFilterProp="label"
                      options={depTaskIdOptions}
                      className="w-full"
                    />
                  </EditableContent>
                </Form.Item>
              </>
            );
          }}
        </Form.Item>
      )}
    </>
  );
};

export default SelectWorkflowPrivate;
