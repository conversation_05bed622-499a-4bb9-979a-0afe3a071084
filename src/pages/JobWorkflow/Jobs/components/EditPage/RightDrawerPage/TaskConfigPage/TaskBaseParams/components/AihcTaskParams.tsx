import {getWorkspaceFileResult} from '@api/WorkArea';
import {Ellipsis} from '@baidu/bce-react-toolkit';
import EditableContent from '@components/EditableContent';
import FilePathSelectWorkarea, {FileNodeTypeEnum} from '@components/FilePathSelectWorkarea';
import {WorkspaceContext} from '@pages/index';
import {IAppState} from '@store/index';
import {Form, Input, Table} from 'acud';
import {FormInstance} from 'acud/lib/form';
import React, {useContext} from 'react';
import {useSelector} from 'react-redux';
import JobGlobalParamsFormItem from '../../../../../FormItem/JobGlobalParamsFormItem';
import RemoteSelect from '@components/RemoteSelect';
import {aihcList, aihcResourcepoolsList} from '@api/workflow';
import AihcTaskParamsSourcesDataFormItem from './formItem/AihcTaskParamsSourcesDataFormItem';

const AihcTaskParams: React.FC<{form?: FormInstance}> = ({form}) => {
  // 是否编辑
  const isEditing = useSelector((state: IAppState) => state.workflowSlice.isEditing);
  const {workspaceId} = useContext(WorkspaceContext);

  const [resourcepool, setResourcepool] = React.useState<string>('');

  const envVars = Form.useWatch('envVars', form);

  const columns = [
    {
      title: '参数名',
      dataIndex: 'name',
      key: 'name',
      width: 70,
      ellipsis: true,
      render: (text: string) => {
        return <Ellipsis tooltip={text}>{text}</Ellipsis>;
      }
    },
    {
      title: '参数值',
      dataIndex: 'value',
      key: 'value',
      width: 100,
      ellipsis: true,
      render: (text: string) => {
        return <Ellipsis tooltip={text}>{text}</Ellipsis>;
      }
    }
  ];

  return (
    <>
      <Form.Item label="资源池">
        <EditableContent isEditing={isEditing}>
          <RemoteSelect
            value={resourcepool}
            onChange={(value) => {
              setResourcepool(String(value));
            }}
            showSearch={true}
            dropdownSearch={true}
            optionFilterProp="label"
            objId="jobId"
            dropdownMatchSelectWidth={false}
            showTitle={true}
            dropdownStyle={{maxWidth: 300}}
            queryList={aihcResourcepoolsList}
            params={[workspaceId]}
            placeholder="请选择"
          />
        </EditableContent>
      </Form.Item>

      <Form.Item
        label="模版训练任务"
        name="templateJobId"
        rules={isEditing ? [{required: true, message: '请选择模版训练任务'}] : []}
      >
        <EditableContent isEditing={isEditing}>
          <RemoteSelect
            showSearch={true}
            dropdownSearch={true}
            optionFilterProp="label"
            objId="jobId"
            dropdownMatchSelectWidth={false}
            showTitle={true}
            dropdownStyle={{maxWidth: 300}}
            queryList={aihcList}
            params={[workspaceId, resourcepool]}
            placeholder="请选择"
          />
        </EditableContent>
      </Form.Item>

      <Form.Item label="任务名称" name="jobName">
        <EditableContent isEditing={isEditing}>
          <Input forbidIfLimit={true} limitLength={256} placeholder="请输入任务名称" allowClear />
        </EditableContent>
      </Form.Item>

      <Form.Item label="执行命令" name="command">
        <EditableContent isEditing={isEditing}>
          <Input placeholder="请输入命令" allowClear />
        </EditableContent>
      </Form.Item>
      <AihcTaskParamsSourcesDataFormItem form={form} isEditing={isEditing} />

      <Form.Item label="环境变量" style={{marginBottom: 0}} name="envs" />
      {isEditing ? (
        <JobGlobalParamsFormItem keyWidth="1 0 150px" formName="envs" keyName="name" />
      ) : (
        <Table dataSource={envVars} columns={columns} pagination={false} />
      )}
    </>
  );
};

export default AihcTaskParams;
