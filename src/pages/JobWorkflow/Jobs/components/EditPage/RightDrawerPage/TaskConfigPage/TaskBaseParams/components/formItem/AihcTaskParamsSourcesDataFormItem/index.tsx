/**
 * 作业 全局参数 表单项
 * 提供列表页面 和 可视化 编辑界面 全局参数 ray 任务 表单项
 * 可以配置 表单名称
 * 默认最多 50 条
 *
 */
import IconSvg from '@components/IconSvg';
import {Button, Col, Form, Input, Row, Table} from 'acud';
import {OutlinedPlus} from 'acud-icon';
import React from 'react';
import styles from './index.module.less';
import EditableContent from '@components/EditableContent';
import {Ellipsis} from '@baidu/bce-react-toolkit';
import {FormInstance} from 'acud/lib/form';
import {AihcTaskDataSourcesType} from '@pages/JobWorkflow/constants';

// 参数名 最大长度
interface IJobListParams {
  isEditing: boolean;
  form: FormInstance;
}
// key value  参数配置
const AihcTaskParamsSourcesDataFormItem: React.FC<IJobListParams> = ({form, isEditing}) => {
  const tableList = Form.useWatch('formName', form);

  const formName = 'dataSources';
  const maxLength = 50;
  // if (!isEditing) {
  //   return (
  //     <Form.Item label={label} name={formName}>
  //       {tableList?.join(',')}
  //     </Form.Item>
  //   );
  // }
  return (
    <>
      <Form.Item label={'数据源'} style={{marginBottom: 0}} />
      <Form.List name={formName}>
        {(fieldsTem, {add, remove}) => {
          // 默认空的时候 也需要展示一条
          const fields = fieldsTem.length === 0 ? [{key: Date.now(), name: 0}] : fieldsTem;
          return (
            <>
              {fields.map(({key, name, ...restField}) => (
                <Row key={key} gutter={8} className={styles['global-params-form-item']}>
                  <Col flex={'1 1 100px'}>
                    <Form.Item {...restField} name={[name, 'type']}>
                      <Input className="w-full" allowClear placeholder="请输入参数名" />
                    </Form.Item>
                  </Col>
                  <Col flex={'0 0 16px'}>
                    {
                      <Button
                        className={styles['btn-delete']}
                        type="text"
                        disabled={fields?.length <= 1}
                        onClick={() => remove(name)}
                      >
                        <IconSvg size={16} type="delete" />
                      </Button>
                    }
                  </Col>

                  <Col span={24}>
                    <Form.Item {...restField} name={[name, 'sourcePath']} label="数据路径">
                      <Input className="w-full" allowClear placeholder="请输入数据路径" />
                    </Form.Item>
                  </Col>
                  <Col span={24}>
                    <Form.Item {...restField} name={[name, 'mountPath']} label="挂载路径">
                      <Input className="w-full" allowClear placeholder="请输入挂载路径" />
                    </Form.Item>
                  </Col>
                </Row>
              ))}

              <Form.Item>
                <Button
                  className={styles['btn-add']}
                  disabled={fields.length >= maxLength}
                  onClick={() =>
                    add({
                      type: AihcTaskDataSourcesType.BOS,
                      name: '',
                      sourcePath: '/',
                      mountPath: '/'
                    })
                  }
                  icon={<OutlinedPlus />}
                  type="actiontext"
                >
                  添加参数
                </Button>
              </Form.Item>
            </>
          );
        }}
      </Form.List>
    </>
  );
};

export default AihcTaskParamsSourcesDataFormItem;
