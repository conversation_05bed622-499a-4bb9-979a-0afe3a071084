import {checkPayInfo} from '@api/billing';
import Enum from '@helpers/enum';
import _ from 'lodash';
export type SceneType = 'NEW' | 'RENEW' | 'DILATATION' | 'SHRINKAGE' | string;

export interface CheckPayInfoPayload {
  orderId: string;
  type: SceneType;
  fromService: string;
  serviceType: string;
  region: string;
  customUrlParams?: {
    [key: string]: any;
  };
}

export interface ParseUrlPayload {
  paramSeperator?: string;
  module: string;
  path: string;
}

export interface OrderUrlPayload {
  orderId?: string;
  orderType: SceneType;
  fromService: string;
  serviceType: string;
  region?: string;
}

export const OrderType = new Enum(
  {alias: 'NEW', text: '新订单', value: 'NEW'},
  // 不同产品的计费变更不太一样
  {alias: 'SHIFT_CHARGE', text: '计费变更', value: 'SHIFT_CHARGE'},
  {alias: 'TO_PREPAY', text: '计费变更', value: 'TO_PREPAY'},
  {alias: 'TO_POSTPAY', text: '计费变更', value: 'TO_POSTPAY'},
  {alias: 'RESIZE', text: '升降配', value: 'RESIZE'},
  {alias: 'RENEW', text: '续费', value: 'RENEW'},
  {alias: 'EIP_RESIZE', text: 'EIP升降配', value: 'EIP_RESIZE'}
);

function getSuccessUrlData(query: OrderUrlPayload): ParseUrlPayload | string {
  const {orderType, serviceType} = query;

  // 续费订单
  if (orderType === OrderType.RENEW) {
    return {module: 'billing', path: '/billing/recharge/success'};
  }
  // 计费变更成功页
  if ([OrderType.SHIFT_CHARGE, OrderType.TO_PREPAY, OrderType.TO_POSTPAY].indexOf(orderType) > -1) {
    return {module: 'billing', path: '/billing/alter/success'};
  }

  return {module: 'billing', path: '/order/success'};
}

export function getPayUrlData(query: OrderUrlPayload): ParseUrlPayload {
  return {
    module: 'finance',
    path: '/finance/pay',
    paramSeperator: '~'
  };
}

export function parseToUrl(url: ParseUrlPayload & {params: object}): string {
  let search = '';
  if (url.params) {
    const params: string[] = [];
    _.forIn(url.params, (v, k) => {
      if (v) {
        params.push(k + '=' + v);
      }
    });
    search += (url.paramSeperator || '~') + params.join('&');
  }
  return `/${url.module}/#${url.path}${search}`;
}

export function getSuccessUrl(params: OrderUrlPayload): string {
  return parseToUrl(_.extend({params}, getSuccessUrlData(params)));
}

export function getPayUrl(params: OrderUrlPayload): string {
  return parseToUrl(_.extend({params}, getPayUrlData(params)));
}

export async function getPayInfoUrl(payload: CheckPayInfoPayload) {
  const {orderId, type, fromService, serviceType, region, customUrlParams} = payload;
  if (!orderId) {
    return Promise.reject('Param Empty');
  }

  const query = {
    orderId,
    serviceType,
    orderType: type,
    fromService,
    region,
    ...customUrlParams,
    // @ts-ignore
    accountId: window.$context ? window.$context.getUserId() : ''
  };

  try {
    const res = await checkPayInfo({orderId});
    if (res.result.isPureCoupon || res.result.orderStatus === 'SUCCESS') {
      return {
        url: getSuccessUrl(query)
      };
    }
    throw 'need to pay';
  } catch (error) {
    return {url: getPayUrl(query)};
  }
}

export function isQasandbox() {
  return window.location.hostname.includes('qasandbox');
}
