.compute-wrapper {
  padding: 0 8px 8px;
  height: 100%;
  box-sizing: border-box;

  .compute-content {
    overflow-y: auto;
    height: 100%;
    box-sizing: border-box;
    position: relative;
    background: #fff;
    border: 1px solid #d4d6d9;
    border-radius: 6px;
    padding: 16px;
  }

  .compute-title {
    font-size: 20px;
    color: #151b26;
    font-weight: bold;
    margin-bottom: 20px;

    .compute-title-icon {
      transform: rotate(180deg);
      cursor: pointer;
      margin-right: 12px;
    }
  }

  .form-wrapper {
    padding: 0 8px;

    .margin-bottom-40px {
      margin-bottom: 40px;
    }
  }
}

.compute-table {
  flex: 1;
  :global {
    .acud-loading-loading-wrapper {
      height: 100%;
      & > div {
        height: 100%;
      }
      .acud-table-empty {
        height: 100%;
        .acud-table-container {
          height: 100%;
          .acud-table-content {
            height: 100%;
            table {
              height: 100%;
            }
          }
        }
      }
    }
  }
}
.compute-status {
  padding: 0 4px;
  display: inline-block;
  border-radius: 10px;
  font-size: 12px;
  line-height: 18px;
  min-width: 52px;
  text-align: center;

  &.deploying {
    background-color: #e6f0ff;
    color: #2468f2;
  }

  &.running {
    background-color: #e7fcec;
    color: #30bf13;
  }

  &.invalid {
    background-color: rgba(7, 12, 20, 0.06);
    color: #5c5f66;
  }

  &.created-fail {
    background-color: #feedea;
    color: #f33e3e;
  }
}
.pagination-wrapper {
  :global {
    .acud-pagination {
      width: fit-content;
    }
  }
}
.blank-space {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  .blank-title {
    font-size: 24px;
    color: #2d2e2e;
    line-height: 32px;
    font-weight: bold;
    text-align: center;
    margin-bottom: 16px;
  }
  .blank-desc {
    font-size: 14px;
    color: #5c5f66;
    text-align: center;
    line-height: 20px;
    margin-bottom: 24px;
  }
  .blank-btn {
    height: 32px;
  }
  .blank-img {
    width: 700px;
    height: 280px;
    background-image: url('~@assets/png/compute/blank-page.png');
    background-size: cover;
    background-repeat: no-repeat;
  }
}
