import {Form} from 'acud';
import styles from './index.module.less';
import classNames from 'classnames/bind';
import {useRegion} from '@hooks/useRegion';
import PaySelect from '@pages/Compute/components/PaySelect';
import {PAY_TYPE} from '@pages/Compute/config';
import OrderTimeRadio from '@pages/Compute/components/OrderTimeRadio';
import useEnv from '@hooks/useEnv';
const cx = classNames.bind(styles);

const payTypeOptions = PAY_TYPE.toArray('POSTPAID', 'PREPAID').map((item) => ({
  ...item,
  value: item.value,
  label: item.text,
  disabled: item.value === PAY_TYPE.PREPAID
}));

export default function usePayAndRegion() {
  const {currentRegion} = useRegion();
  const {isPrivate} = useEnv();

  const renderPayTypeAndRegion = () => {
    if (isPrivate) {
      return null;
    }
    return (
      <>
        <div className={styles['legend-title']}>付费及地域</div>
        <Form.Item name="chargingType" label="付费方式" required>
          <PaySelect options={payTypeOptions as any[]}></PaySelect>
        </Form.Item>
        <Form.Item className={cx('margin-bottom-40px')} name="region" label="地域" required>
          <span>{currentRegion?.label}</span>
        </Form.Item>
      </>
    );
  };

  const renderPayConfig = (chargingType: string) => {
    if (isPrivate) {
      return null;
    }
    return chargingType === PAY_TYPE.PREPAID ? (
      <>
        <div className={styles['legend-title']}>支付设置</div>
        <Form.Item name="orderTime" label="购买时长" required inputMaxWidth="980px">
          <OrderTimeRadio></OrderTimeRadio>
        </Form.Item>
      </>
    ) : null;
  };

  return {
    currentRegion,
    renderPayTypeAndRegion,
    renderPayConfig
  };
}
