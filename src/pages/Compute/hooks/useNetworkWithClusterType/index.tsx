import {useEffect, useState, useMemo} from 'react';
import {useRequest} from 'ahooks';
import {getZoneList, Zone} from '@api/Compute';
import _ from 'lodash';
import {Form, InputNumber, Radio, Select, Link} from 'acud';
import VpcSelect from '@pages/Compute/components/VpcSelect';
import SubnetSelect from '@pages/Compute/components/SubnetSelect';
import styles from './index.module.less';
import classNames from 'classnames/bind';
import useEnv from '@hooks/useEnv';
const cx = classNames.bind(styles);
export interface ZoneOption {
  value: string;
  label: string;
  disabled?: boolean;
  specs: Zone['specs'];
}
interface UseNetworkWithClusterTypeParams {
  engine: string;
  form: any;
  nodeCountInputParams: any;
  nodeTypeOptions?: any;
}

export default function useNetworkWithClusterType(params: UseNetworkWithClusterTypeParams) {
  const {isPrivate} = useEnv();
  const {engine, form, nodeCountInputParams, nodeTypeOptions} = params;
  const [zoneList, setZoneList] = useState<ZoneOption[]>([]);
  const [clusterTypeOptions, setClusterTypeOptions] = useState([]);

  const vpcId = Form.useWatch('vpcId', form);
  const zone = Form.useWatch('availableZone', form);
  const nodeType = Form.useWatch('nodeType', form);

  const {loading: zoneLoading, run: getZoneListRun} = useRequest(getZoneList, {
    manual: true,
    onSuccess: (res) => {
      if (!res.success) {
        return;
      }
      const zoneList = _.map<Zone, ZoneOption>(res.result.zones, (zone) => {
        const zoneName = zone.zoneName?.replace?.(/zone([A-Z])/g, (_, c) => `可用区${c}`);
        return {
          specs: zone.specs,
          value: zone.zoneName,
          label: zoneName
        };
      });
      setZoneList(zoneList);
    }
  });

  useEffect(() => {
    if (engine) {
      getZoneListRun({engine});
    }
  }, [getZoneListRun, engine]);

  useEffect(() => {
    const validZone = zoneList[0];
    if (validZone) {
      form.setFieldsValue({
        availableZone: validZone.value
      });
      onZoneChange(validZone.value);
    }
  }, [zoneList]);

  const onZoneChange = (value: string) => {
    form.setFieldsValue({
      clusterType: ''
    });
    const zone = zoneList.find((item) => item.value === value);
    // 节点规格
    if (zone) {
      const types = _.map(zone.specs, (item) => {
        const disabled = item.inventoryQuantity === 0;
        return {
          value: item.dataBuilderClusterType,
          label: `${item.dataBuilderClusterType} ${disabled ? '(已售罄)' : ''}`,
          type: item.type,
          disabled
        };
      });
      setClusterTypeOptions(types);
    }
  };

  const filteredClusterTypeOptions = useMemo(() => {
    return clusterTypeOptions.filter((option) => option.type === nodeType);
  }, [clusterTypeOptions, nodeType]);

  const onVpcChange = (vpcId?: string) => {
    form.setFieldsValue({
      clusterType: '',
      subnetId: ''
    });
  };

  const onNodeTypeChange = (e) => {
    form.setFieldsValue({
      clusterType: ''
    });
  };

  const zoneName = useMemo(
    () => _.find(zoneList, (item) => item.value === zone)?.label || '',
    [zoneList, zone]
  );

  const vpcRules = [{required: true, message: '请选择网络'}];
  const zoneRules = [{required: true, message: '请选择可用区'}];
  const subnetRules = [{required: true, message: '请选择子网'}];

  const renderNetWorkAndZone = () => {
    if (isPrivate) {
      return null;
    }
    return (
      <>
        <div className={styles['legend-title']}>网络及可用区</div>
        <Form.Item name="vpcId" label="网络" rules={vpcRules}>
          <VpcSelect onChange={onVpcChange}></VpcSelect>
        </Form.Item>
        <Form.Item className="mb-[40px]" label="可用区与子网" required>
          <div className={cx('combine-zone-subnet', 'flex')}>
            <Form.Item className={cx('zone-select')} name="availableZone" rules={zoneRules}>
              <Select loading={zoneLoading} options={zoneList} onChange={onZoneChange}></Select>
            </Form.Item>
            <Form.Item className="flex-1" name="subnetId" rules={subnetRules}>
              <SubnetSelect vpcId={vpcId} zone={zone}></SubnetSelect>
            </Form.Item>
          </div>
          <div className="acud-form-item-extra">
            如需创建新的子网，您可以到
            <Link href="/network/#/vpc/subnet/list" target="_blank" rel="noreferrer">
              私有网络-子网
            </Link>
            创建
          </div>
        </Form.Item>
      </>
    );
  };

  const renderNodeType = ({
    label,
    nodeTypeOptions: nodeTypeOptionsSub
  }: {
    label: string;
    nodeTypeOptions?: any;
  }) => {
    return (
      <Form.Item name="nodeType" label={label || '节点类型'}>
        <Radio.Group
          options={
            nodeTypeOptionsSub ||
            nodeTypeOptions || [
              {value: 'CPU', label: 'CPU'},
              {value: 'GPU', label: 'GPU'}
            ]
          }
          onChange={onNodeTypeChange}
          optionType="button"
        ></Radio.Group>
      </Form.Item>
    );
  };

  const renderClusterType = ({label}: {label: string}) => {
    const clusterTypeRules = [{required: true, message: `请选择${label || '节点规格'}`}];
    return (
      <Form.Item name="clusterType" label={label || '节点规格'} rules={clusterTypeRules}>
        <Select className="w-full" options={filteredClusterTypeOptions}></Select>
      </Form.Item>
    );
  };

  const renderNodeCount = ({label}: {label: string}) => {
    return (
      <Form.Item
        name="nodeCnt"
        label={label || '购买数量'}
        required
        extra="特别提示：为保障服务高可用，建议在生产环境中，数据节点数量不小于3。"
      >
        <InputNumber {...nodeCountInputParams} symmetryMode />
      </Form.Item>
    );
  };

  const renderClusterConfig = () => {
    return (
      <>
        {renderNodeType({label: '节点类型'})}
        {renderClusterType({label: '节点规格'})}
        {renderNodeCount({label: '购买数量'})}
      </>
    );
  };

  return {
    zoneList,
    zoneLoading,
    filteredClusterTypeOptions,
    zoneName,
    onZoneChange,
    renderNetWorkAndZone,
    renderClusterConfig,
    renderNodeType,
    renderClusterType,
    renderNodeCount
  };
}
