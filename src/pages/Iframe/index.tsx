/**
 * iframe 内嵌 EDAP 页面
 */

import React, {FC, useEffect, useLayoutEffect, useState} from 'react';
import './index.less';
import {hideIframe, showIframe} from '@components/IframePreloader';
import * as http from '@api/privateDeliver';
import {Loading} from 'acud';

const IframeEdapPageView: FC = () => {
  const [isLoading, setIsLoading] = useState(true);

  // FIXME： 经过调试，iframe始终达不到预期性能效果，先用这个方式实现，后续结构化列表页面可重新开发
  useLayoutEffect(() => {
    // 主页面代码
    const handleMessage = (event) => {
      if (event.data && event.data.type === 'iframeLoaded') {
        setTimeout(() => {
          // 在延迟 500ms 后关闭 loading 效果
          setIsLoading(false);
        }, 500);
      }
    };

    window.addEventListener('message', handleMessage);

    // 异步请求获取下当前登录状态，未登录直接重定向到登录页
    http.getLoginUserInfo();
    showIframe('edap-iframe', document.getElementById('iframe-div-box') as Element);
    return () => {
      hideIframe('edap-iframe');
      window.removeEventListener('message', handleMessage);
    };
  }, []);

  return (
    <div className="iframe-page-box">
      {isLoading && <Loading loading={true} />}
      <div
        id="iframe-div-box"
        style={{
          width: '100%',
          height: '100%',
          transition: 'opacity 0.3s ease-in-out'
        }}
      />
    </div>
  );
};

export default React.memo(IframeEdapPageView);
