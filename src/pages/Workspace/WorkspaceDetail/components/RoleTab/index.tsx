import React, {useCallback, useEffect, useMemo, useState} from 'react';
import {Button, Modal, Space, Table, toast} from 'acud';
import styles from './index.module.less';
import {EditType, ManagerName, RoleMap} from './constants';
import {Privilege, RoleItem, RoleType} from '@api/permission/type';
import * as http from '@api/permission';
import moment from 'moment';
import EditRoleDrawer from './components/EditRoleDrawer';
import IconSvg from '@components/IconSvg';
import useUrlState from '@ahooksjs/use-url-state';
import classNames from 'classnames';
import {Plus1} from '@baidu/xicon-react-bigdata';
import AuthButton from '@components/AuthComponents/AuthButton';

// 角色
const RoleTab: React.FC<{privilege: Privilege[]; getWorkspaceDetail: () => void}> = ({
  privilege,
  getWorkspaceDetail
}) => {
  const [urlState] = useUrlState({id: ''});
  const [editDrawerOpen, setEditDrawerOpen] = useState<boolean>(false);
  const [editType, setEditType] = useState<EditType>();
  const [drawerInfo, setDrawerInfo] = useState<RoleItem>();
  // const [pagination, setPagination] = useState<{pageNo: number; pageSize: number}>({pageNo: 1, pageSize: 10});
  // const [sorter, setSorter] = useState<{field: JobOrderBy; order: Order}>({
  //   field: JobOrderBy.CreateTime,
  //   order: Order.Desc
  // });
  // const [search, setSearch] = useState('');
  // const [total, setTotal] = useState(0);
  const [roleList, setRoleList] = useState<RoleItem[]>([]);
  const [loading, setLoading] = useState(false);

  const isModify = useMemo(
    () => [Privilege.Modify, Privilege.FullControl].some((item) => privilege.includes(item)),
    [privilege]
  );

  // 获取任务列表
  const getRoleList = useCallback(async () => {
    // const params = {
    //   namePattern: search,
    //   pageNo: pagination.pageNo,
    //   pageSize: pagination.pageSize,
    //   orderBy: sorter.field,
    //   order: orderMap[sorter.order]
    // };
    setLoading(true);
    try {
      const res = await http.getRolesList(urlState.id);
      setRoleList(res.result);
    } catch {
      console.error('获取角色列表失败');
    } finally {
      setLoading(false);
    }
  }, [urlState.id]);

  useEffect(() => {
    getRoleList();
  }, [getRoleList]);

  const onViewRole = useCallback((info) => {
    setEditType(EditType.View);
    setEditDrawerOpen(true);
    setDrawerInfo(info);
  }, []);

  // 编辑角色
  const onEditRole = useCallback((info) => {
    setEditType(EditType.Edit);
    setEditDrawerOpen(true);
    setDrawerInfo(info);
  }, []);
  // 新建角色
  const createRole = useCallback(() => {
    setEditType(EditType.Create);
    setEditDrawerOpen(true);
  }, []);

  const onCloseRoleDrawer = () => {
    setEditDrawerOpen(false);
  };

  const onDeleteRole = useCallback(
    async (name: string) => {
      Modal.confirm({
        title: '删除角色',
        content: '删除角色将导致被绑定的用户无法登录空间，确认是否删除？',
        onOk: async () => {
          try {
            const res = await http.deleteRole(urlState.id, name);
            if (res.result) {
              toast.success({
                message: '删除成功',
                duration: 5
              });
              getRoleList();
              getWorkspaceDetail();
            }
          } catch (err) {
            console.error(err);
          }
        }
      });
    },
    [getRoleList, getWorkspaceDetail, urlState.id]
  );

  const renderOperation = useCallback(
    (record: RoleItem) => {
      const type = record?.type;
      const isEdit = type !== RoleType.System || record?.name !== ManagerName;
      const isDelete = type === RoleType.User;
      return (
        <Space className={styles['operation']}>
          <Button type="actiontext" onClick={() => onViewRole(record)}>
            查看
          </Button>
          <AuthButton
            isAuth={isModify}
            type="actiontext"
            onClick={() => onEditRole(record)}
            disabled={!isEdit}
          >
            编辑
          </AuthButton>
          <AuthButton
            isAuth={isModify}
            type="actiontext"
            onClick={() => onDeleteRole(record.name)}
            disabled={!isDelete}
          >
            删除
          </AuthButton>
        </Space>
      );
    },
    [isModify, onDeleteRole, onEditRole, onViewRole]
  );
  const columns = [
    {
      title: '名称',
      dataIndex: 'name'
    },
    {
      title: '类型',
      dataIndex: 'type',
      width: 100,
      render: (text) => RoleMap[text]
    },
    {
      title: '说明',
      dataIndex: 'description',
      render: (text) => text || '-'
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      // sorter: true,
      width: 200,
      render: (text) => (text ? moment(text).format('YYYY-MM-DD HH:mm:ss') : '-')
    },
    {
      title: '操作',
      dataIndex: 'operation',
      width: 100,
      render: (_, record) => renderOperation(record)
    }
  ];

  // const onPageChange = (page, pageSize) => {
  //   setPagination({
  //     pageNo: page,
  //     pageSize: pageSize
  //   });
  // };

  // const handleTableChange = useCallback((pagination, filters, sorter) => {
  //   setSorter({field: sorter.field, order: sorter.order});
  // }, []);

  return (
    <div>
      <div className={classNames(styles['header'], 'mb-[16px]')}>
        <div className={styles['header-right']}>
          <Button onClick={getRoleList} className="mr-[10px]">
            <IconSvg type="refresh"></IconSvg>
          </Button>
          <AuthButton
            isAuth={isModify}
            icon={<Plus1 theme="line" size={16} strokeLinejoin="round" />}
            onClick={createRole}
          >
            添加角色
          </AuthButton>
        </div>
      </div>

      <Table
        columns={columns}
        dataSource={roleList}
        loading={loading}
        // onChange={handleTableChange}
        size='small'
      />
      <EditRoleDrawer
        type={editType}
        onClose={onCloseRoleDrawer}
        visible={editDrawerOpen}
        info={drawerInfo}
        workspaceId={urlState.id}
        onSuccess={() => {
          getRoleList();
          getWorkspaceDetail();
        }}
      />
    </div>
  );
};

export default RoleTab;
