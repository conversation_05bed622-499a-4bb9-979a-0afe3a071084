.group {
  &-title {
    color: #5c5f66;
    width: 110px;
    font-size: 12px;
    padding: 16px;
    flex-shrink: 0;
  }

  &-item {
    display: flex;
    align-items: center;
    flex: 1;

    &:not(:last-child) {
      border-bottom: 1px solid #e8e9eb;
    }
  }

  &-checkbox {
    padding: 16px;
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    border-left: 1px solid #e8e9eb;
    gap: 16px 0;

    :global(.acud-checkbox-group-item){
      width: calc(100% / 3 - 16px);
    }

    &-disabled {
      pointer-events: none;
    }
  }
}

.not-group-panel {
  color: gray;
  pointer-events: none;

  :global(.acud-collapse-arrow) {
    display: none;
  }

  :global(.acud-collapse-content) {
    border: none;
  }
}

.panel-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.switch {
  pointer-events: auto;

  &-disabled {
    pointer-events: none;
  }
}

:global(.acud-collapse-content > .acud-collapse-content-box) {
  padding: 0;
}

.checked {
  &-icon {
    background: url('../../../../../../../assets/png/check.png');
    background-size: 16px 16px;
    margin-right: 8px;
  }

  &-group {
    display: flex;
    align-items: center;
  }

  &-item {
    display: flex;
    align-items: center;
    color: #151b26;
    font-size: 12px;
    width: 120px;
  }
}
