import React, {useCallback, useEffect, useMemo} from 'react';
import {Checkbox, Collapse, Switch} from 'acud';
import styles from './index.module.less';
import {Privilege} from '@api/permission/type';
import {EditType, PrivilegeCollapseConfig} from '../../constants';
import {getGroupPrivileges} from '../../utils';
import classNames from 'classnames';

const {Panel} = Collapse;

interface PrivilegeCollapseProps {
  type: EditType;
  value?: Privilege[];
  onChange?: (value: Privilege[]) => void;
}

// 角色-功能权限组件
const PrivilegeCollapse: React.FC<PrivilegeCollapseProps> = ({type, value = [], onChange}) => {
  // 菜单权限变化回调
  const onMenuPrivilegeChange = useCallback(
    (checked, menuPrivilege) => {
      const newValue = checked ? [...value, menuPrivilege] : value.filter((item) => item !== menuPrivilege);
      onChange(newValue);
    },
    [onChange, value]
  );

  // 权限点变化回调
  const onPrivilegeItemChange = useCallback(
    (checkedValue, groupName, menuPrivilege) => {
      // 当前 group 全部权限点
      const groupPrivilege = getGroupPrivileges(menuPrivilege, groupName);

      const changedValue = [...value.filter((item) => !groupPrivilege.includes(item)), ...checkedValue];
      // 替换旧数据
      onChange(changedValue);
    },
    [onChange, value]
  );

  const renderPanel = useMemo(() => {
    return PrivilegeCollapseConfig.map((item) => {
      const header = (
        <div className={styles['panel-header']}>
          {item.title}
          <Switch
            checked={value.includes(item.menuPrivilege)}
            onClick={(checked, e) => {
              if (type === EditType.View) {
                return;
              }
              onMenuPrivilegeChange(checked, item.menuPrivilege);
              e.stopPropagation();
            }}
            className={classNames(styles['switch'], {
              [styles['switch-disabled']]: type === EditType.View
            })}
          />
        </div>
      );
      const groups = item?.children;
      const groupMenu = groups?.map((group, index) => {
        const options = group.privilege.map((item) => ({label: item.name, value: item.value}));
        // 当前 group 全部权限点
        const groupPrivilege = getGroupPrivileges(item.menuPrivilege, group.groupName);
        const checkboxValue = value.filter((item) => groupPrivilege.includes(item));
        return (
          <div key={index} className={styles['group-item']}>
            <div className={styles['group-title']}>{group.groupName}</div>
            <Checkbox.Group
              className={classNames(styles['group-checkbox'], {
                [styles['group-checkbox-disabled']]: type === EditType.View
              })}
              options={options}
              value={checkboxValue}
              onChange={(checkedValue) => {
                if (type === EditType.View) {
                  return;
                }
                onPrivilegeItemChange(checkedValue, group.groupName, item.menuPrivilege);
              }}
            />
          </div>
        );
      });
      const className = item?.children?.length ? '' : styles['not-group-panel'];
      return (
        <Panel header={header} key={item.menuPrivilege} className={className} disabled={true}>
          {groupMenu}
        </Panel>
      );
    });
  }, [onMenuPrivilegeChange, onPrivilegeItemChange, type, value]);

  const defaultActiveKey = PrivilegeCollapseConfig.map((item) => item.menuPrivilege);

  return <Collapse defaultActiveKey={defaultActiveKey}>{renderPanel}</Collapse>;
};

export default PrivilegeCollapse;
