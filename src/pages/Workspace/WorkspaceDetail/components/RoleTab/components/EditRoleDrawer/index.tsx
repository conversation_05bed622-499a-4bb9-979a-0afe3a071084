import React, {useCallback, useEffect, useMemo, useState} from 'react';
import {Button, Drawer, Form, Input, toast} from 'acud';
import styles from './index.module.less';
import {DrawerTitle, EditType, RoleMap} from '../../constants';
import PrivilegeCollapse from '../PrivilegeCollapse';
import {RULE} from '@utils/regs';
import {ResourceType, RoleItem, RoleType} from '@api/permission/type';
import * as http from '@api/permission';
import useUrlState from '@ahooksjs/use-url-state';

const nameRules = [
  {required: true, message: '请输入角色名称'},
  {
    validator: (_, value) => {
      // 校验特殊字符和长度限制
      if (!RULE.specialName50.test(value)) {
        return Promise.reject(new Error(RULE.specialName50Text));
      }
      return Promise.resolve();
    }
  }
];

// 角色-新建/编辑抽屉
const EditRoleDrawer: React.FC<{
  type: EditType;
  visible: boolean;
  info?: RoleItem;
  workspaceId: string;
  onClose: () => void;
  onSuccess?: () => void;
}> = ({info, type, visible, workspaceId, onClose, onSuccess}) => {
  const [urlState] = useUrlState({id: ''});
  const [form] = Form.useForm();
  const [loading, setLoading] = useState<boolean>(false);
  const [confirmLoading, setConfirmLoading] = useState<boolean>(false);

  const getRoleDetail = useCallback(async () => {
    if (!info?.name) {
      return;
    }
    setLoading(true);
    try {
      const res = await http.getRoleDetail(urlState.id, info?.name);
      const detail = res.result;
      form.setFieldsValue({
        name: detail?.name,
        description: detail?.description,
        privileges: detail?.privileges
      });
    } catch (err) {
      console.error(err);
    } finally {
      setLoading(false);
    }
  }, [form, info?.name, urlState.id]);

  // 每次打开都重新加载
  useEffect(() => {
    visible && type === EditType.Edit && getRoleDetail();
  }, [getRoleDetail, type, visible]);

  useEffect(() => {
    if (type === EditType.Create) {
      form.resetFields();
    } else {
      getRoleDetail();
    }
  }, [form, getRoleDetail, type]);

  const title = useMemo(() => DrawerTitle[type], [type]);

  const onSubmit = useCallback(async () => {
    try {
      await form.validateFields();
    } catch {
      toast.success({
        message: '请填写名称并选择功能权限！',
        duration: 5
      });
      return;
    }
    const {name, description, privileges} = form.getFieldsValue();

    setConfirmLoading(true);
    try {
      const params = {
        name,
        description,
        privileges,
        ...(type === EditType.Create ? {} : {id: info?.id})
      };
      const res = await http.createRole(workspaceId, params);
      if (res.success) {
        onClose();
        onSuccess();
        form.resetFields();
      }
    } catch (err) {
      console.error(err);
    } finally {
      setConfirmLoading(false);
    }
  }, [form, info?.id, onClose, onSuccess, type, workspaceId]);

  const roleTypeText = useMemo(() => {
    const roleType = type === EditType.Create ? RoleType.User : info?.type;
    return RoleMap?.[roleType];
  }, [info?.type, type]);

  return (
    <Drawer
      onClose={onClose}
      title={title}
      placement="right"
      visible={visible}
      className={styles['drawer']}
      width={800}
      destroyOnClose
      footer={
        <div style={{textAlign: 'right'}}>
          <Button style={{marginRight: 12}} onClick={onClose}>
            取消
          </Button>
          {type === EditType.View ? null : (
            <Button type="primary" onClick={onSubmit} disabled={confirmLoading}>
              提交
            </Button>
          )}
        </div>
      }
    >
      <Form
        form={form}
        labelAlign="left"
        colon={false}
        initialValues={{privileges: []}}
        layout={type === EditType.View ? 'horizontal' : 'vertical'}
      >
        <Form.Item label="名称" name="name" rules={nameRules}>
          {type === EditType.View ? (
            <span className={styles['form-item-value']}>{info?.name}</span>
          ) : (
            <Input />
          )}
        </Form.Item>

        <Form.Item label="类型" name="type">
          <span className={styles['form-item-value']}>{roleTypeText}</span>
        </Form.Item>

        <Form.Item label="任务描述" name="description">
          {type === EditType.View ? (
            <span className={styles['form-item-value']}>{form.getFieldValue('description') ?? '-'}</span>
          ) : (
            <Input.TextArea limitLength={100} placeholder="请输入描述" />
          )}
        </Form.Item>
        <Form.Item
          label="功能权限"
          name="privileges"
          className={styles['collapse']}
          rules={[{required: true, message: '请选择功能权限'}]}
        >
          <PrivilegeCollapse type={type} />
        </Form.Item>
      </Form>
    </Drawer>
  );
};

export default EditRoleDrawer;
