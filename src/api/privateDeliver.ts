/**
 * 私有化环境 对接门户 等需要调用的 API 统一归档此处
 * <AUTHOR>
 */
import {BaseResponseType, request, urlPrefix} from './apiFunction';

export const pms = '/pms';

export interface GetLoginUserInfoRes {
  autoGrant: boolean;
  loginStatus: number;
  cookieInfo: Record<string, any>;
}

export const getLoginUserInfo = async (): Promise<{data: GetLoginUserInfoRes}> => {
  return request.get(`${pms}/v1/user/loginStatus`);
};
