import {API_PREFIX, Request_Method} from '@api/common';
import {BaseResponseType, request} from '@api/apiFunction';
import {
  CreateRoleReq,
  PrincipalType,
  ResourcePermissionInfo,
  ResourceType,
  RoleDetail,
  RoleItem,
  UpdateResourcePermission,
  UpdateWorkspaceUser,
  VerifyAuthParams,
  WorkspaceUserList
} from './type';

/**
 * 获取资源关联的角色列表
 */
export function getRolesList(workspaceId: string): BaseResponseType<RoleItem[]> {
  return request({
    url: `${API_PREFIX}/authorization/roles`,
    method: Request_Method.Get,
    params: {workspaceId}
  });
}

/**
 * 获取资源关联的角色详情
 */
export function getRoleDetail(workspaceId: string, roleName: string): BaseResponseType<RoleDetail> {
  return request({
    url: `${API_PREFIX}/authorization/role`,
    method: Request_Method.Get,
    params: {workspaceId, roleName}
  });
}

/**
 * 创建资源关联的角色
 */
export function createRole(workspaceId, params: CreateRoleReq): BaseResponseType<RoleDetail> {
  return request({
    url: `${API_PREFIX}/authorization/roles`,
    method: Request_Method.Put,
    params: {
      workspaceId
    },
    data: params
  });
}

/**
 * 删除角色
 */
export function deleteRole(workspaceId: string, roleName: string): BaseResponseType<boolean> {
  return request({
    url: `${API_PREFIX}/authorization/roles`,
    method: Request_Method.Delete,
    params: {workspaceId, roleName}
  });
}

/**
 * 获取资源关联的主体角色列表
 */
export function getWorkspaceUserList(params: {
  workspaceId: string;
  principalType?: PrincipalType;
  principalName?: string;
  order?: string;
  pageNo?: number;
  pageSize?: number;
}): BaseResponseType<WorkspaceUserList> {
  return request({
    url: `${API_PREFIX}/authorization/principals`,
    method: Request_Method.Get,
    params
  });
}

/**
 * 获取资源关联的角色详情
 */
export function updateWorkspaceUser(
  workspaceId: string,
  params: UpdateWorkspaceUser[]
): BaseResponseType<boolean> {
  return request({
    url: `${API_PREFIX}/authorization/principals`,
    method: Request_Method.Patch,
    params: {workspaceId},
    data: params
  });
}

const MetaResourceType = [
  ResourceType.Catalog,
  ResourceType.Schema,
  ResourceType.Table,
  ResourceType.Volume,
  ResourceType.Dataset,
  ResourceType.Model,
  ResourceType.Operator,
  ResourceType.Connection
];

/**
 * 查询资源权限列表
 */
export function getResourceList(
  resourceType: ResourceType,
  resourceId: string,
  workspaceId?: string
): BaseResponseType<ResourcePermissionInfo[]> {
  const idParams = MetaResourceType.includes(resourceType) ? {resourceName: resourceId} : {resourceId};
  return request({
    url: `${API_PREFIX}/authorization/acl`,
    method: Request_Method.Get,
    params: {resourceType, ...idParams, workspaceId}
  });
}

/**
 * 更新资源权限列表
 */
export function updateResourcePermission(
  resourceType: ResourceType,
  resourceId: string,
  params: UpdateResourcePermission[],
  workspaceId?: string
): BaseResponseType<boolean> {
  const idParams = MetaResourceType.includes(resourceType) ? {resourceName: resourceId} : {resourceId};
  return request({
    url: `${API_PREFIX}/authorization/acl`,
    method: Request_Method.Patch,
    params: {resourceType, ...idParams, workspaceId},
    data: params
  });
}

/**
 * 资源鉴权
 */
export function verifyAuth(params: VerifyAuthParams): BaseResponseType<boolean> {
  const isWorkspace = params.resource.type === ResourceType.Workspace;
  return request({
    url: `${API_PREFIX}/authorization/verify`,
    method: Request_Method.Post,
    data: params,
    params: isWorkspace ? {workspaceId: params.resource.id} : {}
  });
}

/**
 * 批量资源鉴权
 */
export function batchVerifyAuth(params: VerifyAuthParams[]): BaseResponseType<boolean[]> {
  const isWorkspace = params[0].resource.type === ResourceType.Workspace;
  return request({
    url: `${API_PREFIX}/authorization/verifyBatch`,
    method: Request_Method.Post,
    data: params,
    params: isWorkspace ? {workspaceId: params[0].resource.id} : {}
  });
}

/**
 * 校验是否系统管理员
 */
export function verifySystemAdmin(): BaseResponseType<boolean> {
  return request({
    url: `${API_PREFIX}/authorization/verifySystemAdmin`,
    method: Request_Method.Post
  });
}
