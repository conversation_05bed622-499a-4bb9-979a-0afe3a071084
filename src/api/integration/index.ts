import {BaseResponseType, request} from '@api/apiFunction';
import {isBoolean} from 'lodash';
import {
  BatchOperateType,
  FileJobRunType,
  JobCreateReq,
  JobDetailReq,
  JobUpdateReq,
  JobDetailRes,
  JobNameListRes,
  ListExecutionRes,
  ListExecutionsReq,
  PreCheckResultItem,
  QueryExecutionLogReq,
  QueryExecutionLogRes,
  QueryJobListReq,
  QueryJobListRes,
  RuntimeArgs,
  PreCheckResult
} from './type';
import {API_PREFIX, Request_Method} from '@api/common';
import {handleDownLoad} from '@utils/handleDownLoad';

/**
 * 创建集成任务
 */
export function createIntegrationJob(
  workspaceId: string,
  params?: JobCreateReq
): BaseResponseType<{jobId: string}> {
  return request({
    url: `${API_PREFIX}/workspaces/${workspaceId}/integration/job`,
    method: Request_Method.Post,
    data: params
  });
}

/**
 * 获取任务详情
 */
export function getJobDetails(
  workspaceId: string,
  jobId: string,
  params?: JobDetailReq
): BaseResponseType<JobDetailRes> {
  return request({
    url: `${API_PREFIX}/workspaces/${workspaceId}/integration/job/${jobId}`,
    method: Request_Method.Get,
    params
  });
}

/**
 * 获取集成任务列表
 */
export function getIntegrationJobList(
  workspaceId: string,
  params?: QueryJobListReq
): BaseResponseType<QueryJobListRes> {
  return request({
    url: `${API_PREFIX}/workspaces/${workspaceId}/integration/job`,
    method: Request_Method.Get,
    params
  });
}

/**
 * 启动集成任务
 *
 * 用于启动非结构化同步任务，仅当任务当前处于“未启动”或“已暂停”状态时允许执行。
 * 添加 param 参数
 *    runtimeArgs 运行时参数
 *    isPublished 运行已发布版本还是草稿版本，默认为true, 即执行已发布版本
 */
export function startIntegrationJob(
  workspaceId: string,
  jobId: string,
  param?: {runtimeArgs?: RuntimeArgs; isPublished: boolean}
): BaseResponseType<{runId: string; jobId: string}> {
  const runtimeArgs = param?.runtimeArgs || {};
  const isPublished = isBoolean(param?.isPublished) ? `?isPublished=${param?.isPublished}` : '';

  return request({
    url: `${API_PREFIX}/workspaces/${workspaceId}/integration/job/${jobId}/start${isPublished}`,
    method: Request_Method.Post,
    data: runtimeArgs
  });
}

/**
 * 任务暂停
 *
 * 暂停非结构化同步任务，任务处于运行或者失败状态时，可以进行暂停
 */
export function stopIntegrationJob(
  workspaceId: string,
  jobId: string,
  runId: string
): BaseResponseType<unknown> {
  return request({
    url: `${API_PREFIX}/workspaces/${workspaceId}/integration/execution/${runId}/stop`,
    method: Request_Method.Post
  });
}

/**
 * 获取任务状态
 *
 * 查看非结构化同步任务的状态信息
 */
export function getIntegrationJobStatus(workspaceId: string, jobId: string): BaseResponseType<unknown> {
  return request({
    url: `${API_PREFIX}/workspaces/${workspaceId}/integration/job/${jobId}/suspend`,
    method: Request_Method.Get
  });
}

/**
 * 任务名称列表
 *
 * 获取非结构化同步任务的任务名称列表
 */
export function getJobInfoList(workspaceId: string, namePattern?: string): BaseResponseType<JobNameListRes> {
  return request({
    url: `${API_PREFIX}/workspaces/${workspaceId}/integration/jobInfo`,
    method: Request_Method.Get,
    params: {namePattern}
  });
}

/**
 * 执行记录
 *
 * 获取非结构化同步任务的执行记录
 */
export function listExecutions(
  workspaceId: string,
  jobId: string,
  params: ListExecutionsReq
): BaseResponseType<ListExecutionRes> {
  return request({
    url: `${API_PREFIX}/workspaces/${workspaceId}/integration/job/${jobId}/executions`,
    method: Request_Method.Get,
    params
  });
}

/**
 * 任务更新
 *
 * 更新非结构化同步任务，未存在时报404错误
 */
export function updateIntegrationJob(
  workspaceId: string,
  jobId: string,
  params: JobUpdateReq
): BaseResponseType<{jobId: string}> {
  return request({
    url: `${API_PREFIX}/workspaces/${workspaceId}/integration/job/${jobId}`,
    method: Request_Method.Put,
    data: params
  });
}

/**
 * 任务删除
 *
 * 删除非结构化同步任务
 */
export function deleteIntegrationJob(workspaceId: string, jobId: string): BaseResponseType<unknown> {
  return request({
    url: `${API_PREFIX}/workspaces/${workspaceId}/integration/job/${jobId}`,
    method: Request_Method.Delete
  });
}

/**
 * 任务复制
 *
 * 复制非结构化同步任务
 */
export function copyIntegrationJob(workspaceId: string, jobId: string): BaseResponseType<unknown> {
  return request({
    url: `${API_PREFIX}/workspaces/${workspaceId}/integration/job/${jobId}/copy`,
    method: Request_Method.Post
  });
}

/**
 * 批量操作
 *
 * 批量启动，暂停或者删除任务
 */
export function batchOperationIntegrationJob(
  workspaceId: string,
  operateType: BatchOperateType,
  params: {
    jobIds: string[];
    runtimeArgs?: {
      scheduleTime?: string;
      triggerType?: FileJobRunType;
    };
  }
): BaseResponseType<unknown> {
  return request({
    url: `${API_PREFIX}/workspaces/${workspaceId}/integration/job/batchOperate?operation=${operateType}`,
    method: Request_Method.Post,
    data: params
  });
}

/**
 * 日志查看
 *
 * 获取非机构化同步任务某次执行的日志信息
 */
export function getIntegrationExecutionLog(
  workspaceId: string,
  jobId: string,
  runId: string,
  params: QueryExecutionLogReq
): BaseResponseType<QueryExecutionLogRes> {
  return request({
    url: `${API_PREFIX}/workspaces/${workspaceId}/integration/execution/${runId}/log`,
    method: Request_Method.Get,
    params
  });
}

// 前置检查-validateJob
export function integrationValidateJob(workspaceId: string, jobId: string): BaseResponseType<void> {
  return request({
    url: `${API_PREFIX}/workspaces/${workspaceId}/integration/job/${jobId}/validate`,
    method: Request_Method.Post
  });
}

// 强制通过前置检
export function integrationForcePassValidation(
  workspaceId: string,
  jobId: string
): BaseResponseType<{jobId: string}> {
  return request({
    url: `${API_PREFIX}/workspaces/${workspaceId}/integration/job/${jobId}/forcePassValidation`,
    method: Request_Method.Post
  });
}

// 前置检查结果
export function getValidateResult(workspaceId: string, jobId: string): BaseResponseType<PreCheckResult> {
  return request({
    url: `${API_PREFIX}/workspaces/${workspaceId}/integration/job/${jobId}/validResult`,
    method: Request_Method.Get
  });
}

/**
 * 日志下载
 *
 * 下载非结构化同步任务某次执行的日志文件
 */
export function downloadIntegrationExecutionLog(workspaceId: string, runId: string): void {
  request({
    url: `${API_PREFIX}/workspaces/${workspaceId}/integration/execution/${runId}/log/download`,
    method: Request_Method.Get,
    raw: true,
    responseType: 'blob'
  }).then((res) => handleDownLoad(res));
}
