import {BaseResponseType, request, urlPrefix} from '../apiFunction';
// 训练任务 请求参数
export interface IAihcRequestParams {
  resourcePoolId: string;
  queue?: string;
  keyword?: string;
}

// 训练任务
export interface IAihc {
  jobId: string;
  name: string;
  status: string;
}

// 资源池
export interface IAihcResourcePools {
  metadata: {
    name: string;
    id: string;
  };
}

// 队列
export interface IAihcQueues {
  name: string;
}

/** 获取 白舸 训练任务列表 */
export function aihcList(workspaceId: string, params: IAihcRequestParams): BaseResponseType<IAihc[]> {
  return request({
    url: `${urlPrefix}/workspaces/${workspaceId}/aihc/jobs`,
    method: 'GET',
    data: {...params}
  });
}
/** 获取 白舸 资源列表 */
export function aihcResourcepoolsList(
  workspaceId: string
): BaseResponseType<{resourcePools: IAihcResourcePools[]}> {
  return request({
    url: `${urlPrefix}/workspaces/${workspaceId}/aihc/resourcepools`,
    method: 'GET'
  }).then((res: any) => {
    return {
      ...res,
      result: {
        resourcePools: res?.result?.resourcePools?.map((item: IAihcResourcePools) => {
          return {
            ...item,
            id: item?.metadata?.id,
            name: item?.metadata?.name
          };
        })
      }
    };
  });
}
// 获取 白舸 资源池详情
export function aihcResourcepoolDetail(
  workspaceId: string,
  resourcePoolId: string
): BaseResponseType<IAihcResourcePools> {
  return request({
    url: `${urlPrefix}/workspaces/${workspaceId}/aihc/resourcepool`,
    method: 'GET',
    data: {
      resourcePoolId
    }
  });
}

/** 获取 白舸 队列列表 */
export function aihcQueuesList(
  workspaceId: string,
  resourcePoolId: string
): BaseResponseType<{queues: IAihcQueues[]}> {
  return request({
    url: `${urlPrefix}/workspaces/${workspaceId}/aihc/queues`,
    method: 'GET',
    data: {
      resourcePoolId
    }
  });
}

/** 获取 白舸 队列详情 */
export function aihcQueuesDetail(
  workspaceId: string,
  queue: {
    queueName: string;
    resourcePoolId: string;
  }
): BaseResponseType<IAihcQueues> {
  return request({
    url: `${urlPrefix}/workspaces/${workspaceId}/aihc/queue`,
    method: 'GET',
    data: {
      ...queue
    }
  });
}
