import React, {useMemo} from 'react';
import Main, {workspaceFlattenedMenuList, flattenedMenuList, menus, workspaceMenus} from '../pages';
import {Suspense} from 'react';
import {createHashRouter, Navigate, RouterProvider} from 'react-router-dom';
import urls from '../utils/urls';
import {useAppContext} from '@baidu/bce-react-toolkit';
import {Loading} from 'acud';
import flags from '../flags';
import {withPermissionHoc} from './WithPermissionHoc';

// 仅在非私有化模式下加载激活页面组件
const Activation = !flags.DatabuilderPrivateSwitch
  ? React.lazy(() => import(/* webpackChunkName: "Activation" */ '@pages/Activation'))
  : null;

// 主路由
const mainRouter = flattenedMenuList.map((menu) => ({
  path: menu.key,
  element: <Main menus={menus} component={menu.Component} />,
  privilege: menu.privilege
}));

// 创建一个包装器组件来处理懒加载
const LazyComponent = ({component: Component}: {component?: React.ComponentType<any>}) => {
  return (
    Component && (
      <Suspense
        fallback={
          <div className="db-workspace-wrapper">
            <Loading />
          </div>
        }
      >
        <Component />
      </Suspense>
    )
  );
};

// 工作空间路由 使用二级路由
const workspaceRouter = [
  {
    path: '/workspace',
    id: 'workspace',
    element: <Main menus={workspaceMenus} />,
    children: workspaceFlattenedMenuList.map((menu) => ({
      path: menu.key,
      element: withPermissionHoc(<LazyComponent component={menu.Component} />, menu.privilege),
      privilege: menu.privilege
    }))
  }
];

const RouterComponent = () => {
  const {appState, appDispatch} = useAppContext();

  const router = useMemo(() => {
    const list = [
      ...mainRouter,
      ...workspaceRouter,
      // TODO: 需要修改
      {
        path: '*',
        element: <Navigate to={urls.manageWorkspace} replace />
      }
    ];
    return createHashRouter(list);
  }, [appState.isActivated]);

  return <RouterProvider router={router} />;
};

export default RouterComponent;
