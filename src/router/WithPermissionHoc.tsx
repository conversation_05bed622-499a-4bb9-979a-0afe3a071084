import store, {IAppState} from '@store/index';
import {useSelector} from 'react-redux';
import {WorkspaceMenuPrivileges} from '../pages/index';
import {WorkspaceNoPermission} from '@components/WithoutPermissionPage/WorkspaceNoPermission';
import {PageNoPermission} from '@components/WithoutPermissionPage/PageNoPermission';
import {Loading} from 'acud';
import {PageStatus} from '@store/GlobalAuth';

export const withPermissionHoc = (element, needPermission) => {
  function WithPermissionComponents() {
    const permission = useSelector((state: IAppState) => state.globalAuthSlice.workspacePermission);
    if (!permission) {
      return <Loading />;
    }
    // 是否有空间内菜单的权限
    const hasWorkspaceMenu = WorkspaceMenuPrivileges.some((item) => permission?.[item]);
    // 没有空间内任何菜单的权限，跳转展示空间无权限提示
    if (!hasWorkspaceMenu) {
      store.dispatch({
        type: 'globalAuth/updatePageStatus',
        payload: PageStatus.NoWorkspacePermission
      });
      return <WorkspaceNoPermission />;
    }

    // 判断当前页面是否有权限，如果有权限跳转到当前页面，无权限展示页面无权限页
    if (!needPermission || permission?.[needPermission]) {
      store.dispatch({
        type: 'globalAuth/updatePageStatus',
        payload: PageStatus.Valid
      });
      return element;
    }
    store.dispatch({
      type: 'globalAuth/updatePageStatus',
      payload: PageStatus.NoPagePermission
    });
    return <PageNoPermission />;
  }
  return <WithPermissionComponents />;
};
