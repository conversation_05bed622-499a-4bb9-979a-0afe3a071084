import {PrincipalType, Privilege, ResourcePermissionInfo, ResourceType} from '@api/permission/type';
import * as http from '@api/permission';
import {Button, Dropdown, Link, Search, Select, Space, Table} from 'acud';
import classNames from 'classnames';
import React, {Key, memo, useCallback, useContext, useEffect, useMemo, useState} from 'react';

import styles from './index.module.less';
import PermissionModal from './components/PermissionModal';
import {getMetaUrl, PrivilegeInfo, ResourceConfig} from './ constants';
import {WorkspaceContext} from '@pages/index';
import {useNavigate} from 'react-router-dom';

interface PermissionManageProps {
  resourceType: ResourceType;
  resourceId: string;
  // 是否含有继承对象列
  hasInheritedFrom: boolean;
  // 授权的主体名称，如catalog/schema
  name: string;
  hasAll?: boolean;
  // 当resourceType对应的config不符合预期，需要传特殊的config时使用
  config?: any[];
  manageDescription?: string;
  onSuccess?: () => void;
}

const {Option, OptGroup} = Select;

/**
 * 权限管理
 */
const PermissionManage: React.FC<PermissionManageProps> = ({
  resourceType,
  name,
  resourceId,
  config: resourceConfig,
  hasAll = true,
  hasInheritedFrom = false,
  manageDescription = '',
  onSuccess
}) => {
  const navigate = useNavigate();
  const {workspaceId} = useContext(WorkspaceContext);
  const [search, setSearch] = useState('');
  const [permissionList, setPermissionList] = useState<ResourcePermissionInfo[]>([]);
  const [loading, setLoading] = useState(false);

  const [visible, setVisible] = useState<boolean>(false);
  const [selectedRows, setSelectedRows] = useState<ResourcePermissionInfo[]>([]);
  const [selectedRowKeys, setSelectedRowKeys] = useState<Key[]>([]);
  const [filterPrivilege, setFilterPrivilege] = useState<Privilege[]>();

  // 获取任务列表
  const getPermissionList = useCallback(async () => {
    setLoading(true);
    try {
      const res = await http.getResourceList(resourceType, resourceId, workspaceId);

      setPermissionList(
        res.result.map((item) => ({
          ...item,
          id: `${item.principal.id}-${item.privilege.privilege}-${item?.inheritedFrom?.name}`
        }))
      );
    } catch {
      console.error('获取角色列表失败');
    } finally {
      setLoading(false);
    }
  }, [resourceId, resourceType, workspaceId]);

  useEffect(() => {
    getPermissionList();
  }, [getPermissionList]);

  const onCloseModal = useCallback(() => {
    setVisible(false);
  }, []);

  const onDeletePermission = useCallback(async () => {
    // 删除授权
    const params = selectedRows.map((item) => ({
      principal: item.principal,
      removePrivileges: [item.privilege]
    }));
    try {
      const res = await http.updateResourcePermission(resourceType, resourceId, params, workspaceId);
      if (res.success) {
        getPermissionList();
        setSelectedRows([]);
        setSelectedRowKeys([]);
        onSuccess();
      }
    } catch (err) {
      console.error('授权失败', err);
    }
  }, [getPermissionList, onSuccess, resourceId, resourceType, selectedRows, workspaceId]);

  const onAuth = useCallback(() => {
    // 授权
    setVisible(true);
  }, []);

  const showList = useMemo(() => {
    return permissionList
      .filter((item) => (search ? item.principal.name?.includes(search) : item))
      .filter((item) => {
        return filterPrivilege?.length ? filterPrivilege?.includes(item.privilege.privilege) : item;
      });
  }, [filterPrivilege, permissionList, search]);

  const columns = useMemo(
    () => [
      {
        title: '主体',
        dataIndex: 'principal',
        render: (_, record) => record?.principal?.name
      },
      {
        title: '权限',
        dataIndex: 'privilege',
        render: (_, record) => PrivilegeInfo[record.privilege.privilege]?.name
      },
      ...(hasInheritedFrom
        ? [
            {
              title: '继承对象',
              dataIndex: 'inheritedFrom',
              render: (_, record) =>
                record?.inheritedFrom?.name ? (
                  <Link onClick={() => navigate(getMetaUrl(record?.inheritedFrom?.name, workspaceId))}>
                    {record?.inheritedFrom?.name}
                  </Link>
                ) : (
                  // 如果继承对象为空就是当前主体，不可点击
                  name
                )
            }
          ]
        : [])
    ],
    [hasInheritedFrom, name, navigate, workspaceId]
  );

  const handleChange = useCallback((value: Privilege[]) => {
    setFilterPrivilege(value);
  }, []);

  const config = useMemo(
    () => resourceConfig || ResourceConfig[resourceType].config,
    [resourceConfig, resourceType]
  );

  const privilegeFilter = useMemo(() => {
    if (!permissionList?.length) {
      return null;
    }
    // 列表中全部权限点
    const list = permissionList.map((item) => item.privilege.privilege);
    const optionConfig = [PrivilegeInfo[Privilege.All], PrivilegeInfo[Privilege.Manage], ...config]
      .map((item) => {
        if (item?.groupName) {
          // 分组
          const filtered = item.privilege.filter((item) => list.includes(item.privilege));
          if (filtered.length === 0) return null; // 整个分组无权限，隐藏
          return {
            groupName: item.groupName,
            privilege: filtered
          };
        } else {
          // 非分组（平铺）
          return list.includes(item.privilege) ? item : null;
        }
      })
      .filter(Boolean);
    return (
      <Select style={{width: 200}} allowClear mode="multiple" onChange={handleChange} className="mr-[12px]">
        {optionConfig.map((group) =>
          group?.groupName ? (
            <Select.OptGroup
              key={group.groupName}
              label={group.groupName}
              defaultExpandGroupKey={config.map((item) => item.groupName)}
            >
              {group?.privilege?.map((item) => {
                return (
                  <Option value={item.privilege} key={item.privilege}>
                    {item.name}
                  </Option>
                );
              })}
            </Select.OptGroup>
          ) : (
            <Option value={group.privilege} key={group.privilege}>
              {group.name}
            </Option>
          )
        )}
      </Select>
    );
  }, [config, handleChange, permissionList]);

  const onTableChange = useCallback(() => {
    setSelectedRowKeys([]);
    setSelectedRows([]);
  }, []);

  // 权限更新成功回调
  const onSubmitSuccess = useCallback(() => {
    getPermissionList();
    onSuccess && onSuccess();
  }, [getPermissionList, onSuccess]);

  return (
    <div>
      <div className={classNames(styles['header'], 'mb-[16px]')}>
        <Search
          onChange={(e) => setSearch(e.target.value)}
          placeholder="请输入主体名称进行搜索"
          style={{width: 240}}
        />
        <Space>
          {privilegeFilter}
          <Button onClick={onDeletePermission} className="mr-[10px]" disabled={!selectedRowKeys?.length}>
            取消授权
          </Button>
          <Button onClick={onAuth}>授权</Button>
        </Space>
      </div>

      <Table
        columns={columns}
        dataSource={showList}
        loading={loading}
        rowKey="id"
        onChange={onTableChange}
        rowSelection={{
          getCheckboxProps: (record) => ({
            // 系统角色不可撤销
            // 继承来的权限不可撤销
            disabled: hasAll ? !!record?.inheritedFrom?.name : record.principal.type === PrincipalType.Role
          }),
          selectedRowKeys,
          onChange: (selectedRowKeys, selectedRows) => {
            setSelectedRowKeys(selectedRowKeys);
            setSelectedRows(selectedRows);
          }
        }}
      />

      <PermissionModal
        visible={visible}
        onCancel={onCloseModal}
        onSuccess={onSubmitSuccess}
        resourceType={resourceType}
        name={name}
        resourceId={resourceId}
        hasAll={hasAll}
        config={config}
        manageDescription={manageDescription}
      />
    </div>
  );
};

export default memo(PermissionManage);
