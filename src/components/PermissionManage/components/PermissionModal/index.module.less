.group {
  margin-bottom: 24px;

  &-title {
    font-weight: 500;
    font-size: 12px;
    line-height: 20px;
    color: #151b26;
    margin-bottom: 12px;
  }

  &-checkbox {
    display: flex;
    flex-wrap: wrap;
    row-gap: 12px;
  }
}

.checkbox-content {
  border-bottom: 1px solid #d9d9d9;
  margin-bottom: 24px;
  margin-top: 20px;
}

.checkbox {
  &-item {
    display: flex;
    margin-bottom: 12px;
  }

  &-description {
    font-size: 12px;
    line-height: 20px;
    color: #84868c;
  }

  &-wrapper {
    width: 33%;

    &:global(.acud-checkbox-wrapper) {
      margin-left: 0;
    }
  }

  &-label {
    padding: 3px 6px;
    background-color: #f7f7f9;
    color: #151b26;
    border-radius: 4px;
  }
}

.option {
  font-size: 12px;
  display: flex;
  justify-content: space-between;

  &-name {
    color: #151b26;
  }

  &-tag {
    color: #84868c;
    flex: unset !important;
  }
}
