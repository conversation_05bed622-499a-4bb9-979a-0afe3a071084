import {Button, Tooltip} from 'acud';
import {ButtonProps} from 'acud/lib/button';
import {isNil} from 'lodash';
import React, {memo, useMemo} from 'react';
import {TooltipConfig, TooltipType} from './constants';

interface AuthButtonProps extends ButtonProps {
  // 是否有授权
  isAuth: boolean;
  tooltipType?: TooltipType;
}

/**
 * 包含鉴权和 tooltip 提示按钮，与普通 Button 用法参数一致，需要传入鉴权参数
 */
const AuthButton: React.FC<AuthButtonProps> = ({
  isAuth,
  tooltipType = TooltipType.Resource,
  children,
  disabled: propsDisabled,
  ...props
}) => {
  const disabled = useMemo(() => {
    // 没有传 disable，只需要判断 auth
    if (isNil(propsDisabled)) {
      return !isAuth;
    }
    return propsDisabled || !isAuth;
  }, [isAuth, propsDisabled]);

  const button = useMemo(
    () => (
      <Button {...props} disabled={disabled}>
        {children}
      </Button>
    ),
    [children, disabled, props]
  );

  return <>{isAuth ? button : <Tooltip title={TooltipConfig[tooltipType]}>{button}</Tooltip>}</>;
};

export default memo(AuthButton);
