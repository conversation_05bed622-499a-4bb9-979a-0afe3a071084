import {Tooltip} from 'acud';
import React, {cloneElement, memo, ReactElement} from 'react';
import {TooltipConfig, TooltipType} from './constants';

interface AuthComponentsProps {
  // 是否有授权
  isAuth: boolean;
  children: ReactElement;
  placement?: 'top' | 'bottom' | 'left' | 'right';
  tooltipType?: TooltipType;
}

/**
 * 包含鉴权和 tooltip
 */
const AuthComponents: React.FC<AuthComponentsProps> = ({
  isAuth,
  tooltipType = TooltipType.Resource,
  children,
  placement = 'top'
}) => {
  if (isAuth) {
    return children;
  }
  // 克隆 children
  const controlledChild = cloneElement(children, {
    disabled: !isAuth
  });
  return (
    <>
      <Tooltip title={TooltipConfig[tooltipType]} placement={placement}>
        {controlledChild}
      </Tooltip>
    </>
  );
};

export default memo(AuthComponents);
