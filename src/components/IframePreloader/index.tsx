/**
 * iframe预加载管理器组件
 * 在应用启动时创建并管理所有需要预加载的iframe , 后续重做，不采用 iframe形式，效果还是不佳
 * <AUTHOR>
 */

import React, {useEffect, useState} from 'react';

const EdapIframeURL = window?.PRIVATE_STATIC?.mainPageConfig?.EdapIframeURL;

/**
 * 通过 webpack.DefinePlugin 注入的全局变量 IS_DEV_ENV 判断环境
 * 本地环境
 *  - 按常规处理：去掉 localhost. 和 端口
 * 生产环境
 *  - 优先使用 private-config.js 中配置的 EdapIframeURL
 *  - 默认使用 window.location.origin + /edap/ 【要求部署的时候域名要相同】
 */
export const edapIframeSrc = IS_DEV_ENV
  ? window.location.origin
      .replace(/^((https?):\/\/)localhost\./, '$1') // 去掉 localhost.
      .replace(/:\d+$/, '') + '/edap/' // 去掉端口号
  : EdapIframeURL || `${window.location.origin}/edap/`;

interface IframeConfig {
  id: string;
  src: string;
  preload: boolean;
  targetPath?: string;
}

// iframe配置
const IFRAME_CONFIGS: IframeConfig[] = [
  {
    id: 'edap-iframe',
    src: edapIframeSrc,
    preload: true,
    targetPath: '/edap-workspace'
  }
];

interface IframePreloaderProps {
  children?: React.ReactNode;
}

const IframePreloader: React.FC<IframePreloaderProps> = ({children}) => {
  useEffect(() => {
    // 创建需要预加载的iframe
    IFRAME_CONFIGS.forEach((config) => {
      if (!config.preload) return;

      // 检查iframe是否已存在
      let iframe = document.getElementById(config.id) as HTMLIFrameElement;

      if (!iframe) {
        // 创建新的iframe
        iframe = document.createElement('iframe');
        iframe.id = config.id;
        iframe.src = config.src;
        iframe.style.cssText = `
          display: none;
          width: 100%;
          height: 100vh;
          border: none;
          position: absolute;
          top: 0;
          left: 0;
        `;

        // 添加加载事件监听
        const handleLoad = () => {
          console.log(`[EDAP] iframe 提前加载 ${config.id} loaded successfully`);
        };

        const handleError = () => {
          console.error(`[EDAP] iframe 提前加载 ${config.id} failed to load`);
        };

        iframe.addEventListener('load', handleLoad);
        iframe.addEventListener('error', handleError);

        // 将iframe添加到body
        document.body.appendChild(iframe);

        // 清理函数
        return () => {
          iframe.removeEventListener('load', handleLoad);
          iframe.removeEventListener('error', handleError);
        };
      }
    });

    // 清理函数：在组件卸载时移除所有预加载的iframe
    return () => {
      IFRAME_CONFIGS.forEach((config) => {
        if (config.preload) {
          const iframe = document.getElementById(config.id);
          if (iframe && iframe.parentNode) {
            iframe.parentNode.removeChild(iframe);
          }
        }
      });
    };
  }, []);

  return <>{children}</>;
};

export default IframePreloader;

// 导出配置和工具函数
export {IFRAME_CONFIGS};

// 获取iframe加载状态的工具函数
export const getIframeLoadStatus = (iframeId: string): boolean => {
  const iframe = document.getElementById(iframeId) as HTMLIFrameElement;
  if (!iframe) return false;

  try {
    return iframe.contentDocument?.readyState === 'complete';
  } catch (error) {
    // 跨域情况下无法访问contentDocument
    return true; // 假设已加载
  }
};

// 显示指定iframe的工具函数
export const showIframe = (iframeId: string, container: Element): boolean => {
  const iframe = document.getElementById(iframeId) as HTMLIFrameElement;
  if (!iframe || !container) return false;

  if (iframe.parentNode !== container) {
    container.appendChild(iframe);
  }

  iframe.style.display = 'block';
  iframe.style.width = '100%';
  iframe.style.height = '100%';

  return true;
};

// 隐藏指定iframe的工具函数
export const hideIframe = (iframeId: string): boolean => {
  const iframe = document.getElementById(iframeId) as HTMLIFrameElement;

  if (!iframe) return false;

  iframe.style.display = 'none';

  // 将iframe移回body以保持预加载状态
  if (iframe.parentNode !== document.body) {
    document.body.appendChild(iframe);
  }

  return true;
};
