import React, {useMemo, useState} from 'react';
import {Button} from 'acud';
import './index.less';
import flags from '@/flags';
import {useRegion} from '@/hooks/useRegion';
const isPrivate = flags?.DatabuilderPrivateSwitch;

const EdapEntry: React.FC = () => {
  const {currentRegion} = useRegion();
  const [showFlag, setShowFlag] = useState<boolean>(window._edapShow);
  const goEdap = () => {
    window.open('/edap/', '_blank');
  };
  const handleClose = () => {
    window._edapShow = false;
    setShowFlag(false);
  };
  const showEntry = useMemo(() => {
    const availableRegion = ['bj', 'bd', 'global'];
    return !isPrivate && availableRegion.includes(currentRegion.id) && showFlag;
  }, [currentRegion, showFlag]);
  return showEntry ? (
    <div className="edap-entry">
      <div className="edap-entry-content mt-[8px]">
        <div className="top-container">
          <span className="top-container-title mb-[8px]">
            <span>前往结构化数据治理中心</span>
          </span>
          <Button className="goButton ml-[8px]" onClick={goEdap}>
            立即使用 <div className="entry-icon"></div>
          </Button>
        </div>
      </div>
      <Button className="edap-entry-close" onClick={handleClose} type="actiontext">
        x
      </Button>
    </div>
  ) : (
    <></>
  );
};
export default EdapEntry;
