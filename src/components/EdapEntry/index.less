.edap-entry {
  position: relative;
  width: 100%;
  height: 48px;
  display: flex;
  justify-content: space-around;
  background: url(../../assets/png/workspace/edap_entry_banner.png) no-repeat;
  background-size: cover;
  align-items: center;
  z-index: 10000;
  &-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    .top-container {
      display: flex;
      align-items: baseline;
      &-title {
        font-size: 16px;
        color: #151b26;
        font-family: PingFangSC-Medium;
        .bolder-title {
          font-size: 18px;
        }
      }
      .goButton {
        width: 100px;
        height: 25px;
        border-radius: 14px;
        margin-left: 10px;
        padding: 0 12px;
        cursor: pointer;
        border: none;
        box-sizing: border-box;
        font-size: 14px;
        font-weight: 500;
        color: #fff;
        background: linear-gradient(270deg, #4fc8fd 0%, #0967ff 30%);
        transition: all 0.1s;
        display: flex;
        align-items: center;
        .entry-icon {
          margin-left: 4px;
          width: 12px;
          height: 12px;
          background-image: url(../../assets/svg/entry_more.svg?url);
        }
        &:hover {
          transform: scale(1.01);
        }
      }
    }
    .bottom-description {
      font-family: PingFangSC-Regular;
      font-size: 12px;
      color: #5c5f66;
      line-height: 20px;
      .detail-link {
        cursor: pointer;
        text-decoration: underline;
        &:hover {
          color: #2468f2;
        }
      }
    }
  }
  &-close {
    position: absolute;
    right: 24px;
    padding: 0;
    margin: 0;
  }
}
