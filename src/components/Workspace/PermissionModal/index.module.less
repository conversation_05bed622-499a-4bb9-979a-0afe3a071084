.permission-modal {
  :global {
    .acud-modal-title {
      width: 100%;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      padding-right: 24px;
    }
    .acud-modal-body {
      overflow: visible;
    }
  }
  .modal-content {
    height: 400px;
  }
}

.step1 {
  height: 100%;
  .user-list {
    height: calc(100% - 54px);
    overflow-y: auto;
    margin-right: -20px;
    padding-right: 20px;
  }

  .user-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 40px;

    .user-info {
      display: flex;
      align-items: center;
      gap: 12px;

      .principal-name {
        max-width: 300px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .principal-type {
        width: 28px;
        height: 28px;
        background-size: 28px 28px;
        background-repeat: no-repeat;
        background-position: center;
        &.USER {
          background-image: url('~@assets/originSvg/user.svg?url');
        }
        &.GROUP {
          background-image: url('~@assets/originSvg/user-group.svg?url');
        }
        &.ROLE {
          background-image: url('~@assets/originSvg/user-manager.svg?url');
        }
        &.OWNER {
          background-image: url('~@assets/originSvg/user.svg?url');
        }
      }
    }
  }
}

.step2 {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;

  .select-wrap {
    display: flex;
    gap: 12px;
    .user-select {
      flex: 1;
    }
  }

  .btn-wrap {
    display: flex;
    gap: 12px;
    justify-content: flex-end;
  }
}

.user-select-dropdown {
  .user-name {
    display: flex;
    align-items: center;
    gap: 4px;
  }
  .user-icon {
    width: 16px;
    height: 16px;
    flex: none;
  }
  .user-name-text {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

.select-privilege {
  .select-privilege-text {
    display: flex;
    align-items: center;
    gap: 12px;
  }
  .arrow {
    transform: rotate(180deg);
    color: #84868c;
  }
  :global(.acud-dropdown-open) {
    .arrow {
      transform: rotate(0deg);
    }
  }
}
.privilege-dropdown-menu {
  .dropdown-menu {
    width: 100px;
  }
}
