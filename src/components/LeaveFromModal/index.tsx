import IconSvg from '@components/IconSvg';
import {Button, Modal, Space} from 'acud';
import {useMemoizedFn} from 'ahooks';
import React, {useEffect, useState} from 'react';
import {useBlocker} from 'react-router-dom';
import './index.less';
/**
 * 离开页面确认弹窗
 * isEditing 是否处于编辑状态
 * leaveClickTime 离开页面点击时间戳 如果大于0 则显示弹窗(页面主动跳出)
 * onClickAndRefresh 离开编辑状态，但是还保持在当前页
 * onSave  保存方法  需要返回成功或失败的promise对象
 * title 提示信息
 */
interface ILeaveModalProps {
  leaveClickTime: number;
  isEditing: boolean;
  onClickAndRefresh?: () => void;
  onSave: () => Promise<boolean>;
  title?: string;
}
const LeaveFromModal: React.FC<ILeaveModalProps> = ({
  leaveClickTime,
  isEditing,
  onClickAndRefresh,
  onSave,
  title = '当前数据暂未保存，如果现在关闭，未保存的数据内容将丢失。请确认是否要继续关闭'
}) => {
  const [visible, setVisible] = useState(false);
  const [loading, setLoading] = useState(false);

  // 监听页面是否处于编辑状态
  const blocker = useBlocker(
    ({currentLocation, nextLocation}) => isEditing && currentLocation.pathname !== nextLocation.pathname
  );
  // 监听页面是否处于编辑状态 如果处于编辑状态，则显示弹窗
  useEffect(() => {
    if (blocker.state === 'blocked') {
      setVisible(true);
    }
  }, [blocker]);
  // 点击x弹窗
  const closeModal = useMemoizedFn(() => {
    setVisible(false);
    if (blocker?.state === 'blocked') {
      blocker?.reset();
    }
  });
  // 取消弹窗
  const handleCancel = useMemoizedFn(() => {
    setVisible(false);
    if (blocker?.state === 'blocked') {
      blocker?.proceed();
    } else {
      onClickAndRefresh?.();
    }
  });

  // 保存并关闭
  const handleOk = useMemoizedFn(async () => {
    setLoading(true);
    try {
      const res = await onSave();
      // 如果保存成功 则关闭弹窗
      if (res) {
        if (blocker?.state === 'blocked') {
          blocker?.proceed();
        } else {
          onClickAndRefresh?.();
        }
      }
    } catch (error) {}
    setVisible(false);
    setLoading(false);
  });

  useEffect(() => {
    if (leaveClickTime) {
      setVisible(true);
    }
  }, [leaveClickTime]);
  return (
    <Modal
      title={
        <Space>
          <IconSvg type="warning" color="#FF9326" size={22} />
          <span>关闭确认</span>
        </Space>
      }
      className={'leave-form-modal'}
      width={400}
      visible={visible}
      onCancel={closeModal}
      footer={
        <div className="w-full">
          <Button className={'float-left'} onClick={closeModal}>
            取消
          </Button>
          <Space>
            <Button onClick={handleCancel}>立即关闭</Button>
            <Button type="primary" onClick={handleOk} loading={loading}>
              保存并关闭
            </Button>
          </Space>
        </div>
      }
    >
      {title}
    </Modal>
  );
};

export default LeaveFromModal;
