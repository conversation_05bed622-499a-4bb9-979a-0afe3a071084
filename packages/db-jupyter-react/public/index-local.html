<!--
  ~ Copyright (c) 2021-2023 Datalayer, Inc.
  ~
  ~ MIT License
-->

<!DOCTYPE html>
<html>

<head>
  <title><PERSON><PERSON><PERSON> React Example</title>
  <!-- <script id="datalayer-config-data" type="application/json">
    {
      "jupyterServerUrl": "http://localhost:8686/api/jupyter-server",
      "jupyterServerToken": "60c1661cc408f978c309d04157af55c9588ff9557c9380e4fb50785750703da6",
      "runUrl": "https://prod1.datalayer.run",
      "token": "",
      "cpuEnvironment": "python-cpu-env",
      "gpuEnvironment": "pytorch-gpu-env",
      "credits": 1
    }
  </script>
  <script id="jupyter-config-data" type="application/json">
    {
      "appName": "<PERSON><PERSON><PERSON> React",
      "baseUrl": "http://localhost:8686/api/jupyter-server",
      "wsUrl": "ws://localhost:8686/api/jupyter-server",
      "token": "60c1661cc408f978c309d04157af55c9588ff9557c9380e4fb50785750703da6",
      "appUrl": "/lab",
      "themesUrl": "/lab/api/themes",
      "disableRTC": false,
      "terminalsAvailable": "false",
      "mathjaxUrl": "https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.5/MathJax.js",
      "mathjaxConfig": "TeX-AMS_CHTML-full,Safe"
    } -->
  </script>
  <script
    data-jupyter-widgets-cdn="https://cdn.jsdelivr.net/npm/"
    data-jupyter-widgets-cdn-only="true"
  >
  </script>
  <link rel="shortcut icon"
    href="data:image/x-icon;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAAN1wAADdcBQiibeAAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAAAC7SURBVFiF7ZU9CgIxEIXfTHbPopfYc+pJ9AALtmJnZSOIoJWFoCTzLHazxh/Ebpt5EPIxM8XXTCKTxYyMCYwJFhOYCo4JFiMuu317PZwaqEBUIar4YMmskL73DytGjgu4gAt4PDJdzkkzMBloBhqBgcu69XW+1I+rNSQESNDuaMEhdP/Fj/7oW+ACLuACHk/3F5BAfuMLBjm8/ZnxNvNtHmY4b7Ztut0bqStoVSHfWj9Z6mr8LXABF3CBB3nvkDfEVN6PAAAAAElFTkSuQmCC"
    type="image/x-icon" />
</head>

<body>
</body>

</html>
