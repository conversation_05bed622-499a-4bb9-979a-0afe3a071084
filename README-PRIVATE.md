# DataBuilder 私有化部署说明

## 一. 项目简介

本项目基于 [@baidu/cba-cli](https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/tQvrZcAEPS/4yidKCir54/Yl586IZSCNxzLl) 脚手架，适配百度智能云 DataBuilder 的私有化部署需求。整体技术栈与公有云保持一致，并且**代码同源**，基于 React 18、acud 组件库、Redux、React Router 构建。

在私有化场景下，使用 `private-common/bce-config.js` 配置文件，支持菜单、接口、功能开关（flags）等定制化，构建产物可部署于指定私有环境。

---

## 二. 私有化环境准备

### 2.1 安装依赖

- 要求 nodejs >= 20.18.1，npm >=10.7.0

```bash
npm install
# 如遇依赖冲突，请优先调整依赖版本，避免使用 `--legacy-peer-deps`。
```

---

## 三、本地开发与联调

### 3.1 如何定制联调环境

如需接入指定联调环境

- 1、请先访问对应环境 http:xxx/databuilder/ 环境；
- 2、然后请修改 `.env` 中以下配置项：

```shell
# 对应本地 host 域名，例如：
DEV_HOST=localhost.bigdata.agilecloud-yq.com

# 对应本地服务端口
DEV_PORT=8897

# 对应后端联调环境地址（协议+域名+端口），例如：
PROXY_TARGET=http://bigdata.agilecloud-yq.com
```

> 注意：`DEV_HOST` 和 `PROXY_TARGET的域名` 应有本地 hosts 文件配置，确保浏览器可访问。

修改后重启本地服务，即可实现与指定后端环境的联调

### 3.2 启动本地私有化开发环境

```bash
npm run dev:private
```

- 默认加载 `private-common/bce-config.js` 配置。
- 会根据配置自动生成运行时 flags，无需手动维护。

---

## 四、私有化构建&部署

## 4.1 私有化流水线构建说明

本项目在 [IPipe 流水线](https://console.cloud.baidu-int.com/devops/ipipe/workspaces/435118/pipelines/2493280/builds/list?branchName=branches) 中，已针对私有化场景配置了专用的构建流程。请参考 `ci.yml` 文件中的 `build_DatabuilderPrivate` profile字段

**构建命令**：

```shell
sh scripts/build.sh private-common ${AGILE_COMPILE_BRANCH} # ${AGILE_COMPILE_BRANCH} 为流水线变量，代表当前编译的分支名称
```

如需在流水线外部手动模拟私有化构建，可本地执行：

```shell
sh scripts/build.sh private-common <your-branch-name>
```

---

## 4.2 私有化产物说明

### `public/` 目录说明

构建时会自动通过 `@baidu/cba-cli` 中webpack配置，将 `public/` 目录下内容拷贝至最终产物目录。

- `fe-framework.css` 样式文件保持与公有云一致，支持统一维护。
  - 注：私有化环境中，默认注释了 `#main {}` 样式，避免影响私有页面布局。
- `private-config.js` 和`favicon.ico` 亦会自动复制，无需额外处理。

### `_VERSION.js` 文件说明

构建完成后，产物目录 `output/` 下会生成 `_VERSION.js` 文件，记录当前构建的版本信息，内容包含：

1. 当前构建分支名（如：`feature/xxx`、`master`）
2. 构建时间（格式：`YYYY-MM-DD HH:mm:ss`）
3. 固定标识 “私有化Databuilder”

此文件便于后续部署版本回溯、问题定位等使用。

### `private-config.js` 文件说明

`public/private-config.js` 文件用于大数据 DataBuilder 私有化交付产品的静态配置，支持结合天牛平台完成常见定制化能力。

## 4.3 docker 目录与私有化镜像发布说明

`docker/` 目录下的内容（如 `Dockerfile`、`start.sh`）为流水线构建发布镜像时专用，配合天牛平台实现私有化部署：

- `Dockerfile` 以公司官方 nginx 镜像为基础，自动复制构建产物和启动脚本，适配多 CPU 架构。
- 镜像构建产物目录在docker容器中为 `/usr/share/output`，启动脚本为 `/root/start.sh`。
- 天牛平台可直接使用该镜像进行私有化部署，无需额外调整。
- 相关配置和注意事项已在 `Dockerfile` 注释中说明。

---

## 五、flags 使用说明

私有化环境会根据 `private-common/bce-config.js` 中的 `flags` 配置（这里默认只拉取 DB 产品的功能清单），然后在项目运行开始时自动更新 `src/flags.ts` 文件内容。

- flags 文件为运行时文件，在每次项目启动或打包时都会根据配置文件中的templateId和flags重新生成。

业务中使用方式，示例如下：

```javascript
import flags from '@/flags';
// 通过 DatabuilderPrivateSwitch 属性, 来判断是否为私有化环境
const isPrivate = flags.DatabuilderPrivateSwitch;
// 业务逻辑使用该变量来调整功能
if (isPrivate) {
  // 私有化环境下的逻辑
}
```

## 六、业务中特殊逻辑说明

### 1、业务侧使用 API 请求封装都要从 `@baidu/bce-react-toolkit` 中导入，改为从 `src/api/apiFunction.tsx` 中导入

```javascript
// ❌ 不要使用 @baidu/bce-react-toolkit 导出的 request
// import {request} from '@baidu/bce-react-toolkit';

// ✅ 从 src/api/apiFunction.tsx 中导入
import {request, BaseResponseType, urlPrefix} from './apiFunction';
```

### 2、业务侧使用 useRegion 钩子都要从 `@baidu/bce-react-toolkit` 中导入，改为从 `src/hooks/useRegion.ts` 中导入

```javascript
// ❌ 不要使用 @baidu/bce-react-toolkit 导出的 useRegion
// import {useRegion} from '@baidu/bce-react-toolkit';

// ✅ 从 src/hooks/useRegion.ts 中导入
import {useRegion} from '@hooks/useRegion';
```
